<?php
/**
 * Proxmox Transfer Checker - 定期检查和修复数据不一致
 * 
 * 这个脚本可以作为cron job运行，定期检查和修复
 * WHMCS服务转移后Proxmox数据不同步的问题
 * 
 * 使用方法：
 * 1. 上传到WHMCS根目录
 * 2. 设置cron job: php -q /path/to/whmcs/proxmoxTransferChecker.php
 * 3. 建议每小时运行一次
 */

// 包含WHMCS配置
require_once __DIR__ . '/init.php';

use WHMCS\Database\Capsule;

class ProxmoxTransferChecker {
    
    private $logPrefix = "ProxmoxTransferChecker";
    private $dryRun = false; // 设置为true进行测试运行，不实际修改数据
    
    public function __construct($dryRun = false) {
        $this->dryRun = $dryRun;
    }
    
    /**
     * 主要检查和修复函数
     */
    public function checkAndFix() {
        $this->log("Starting Proxmox transfer data consistency check...");
        
        $issues = $this->findInconsistencies();
        
        if (empty($issues)) {
            $this->log("No inconsistencies found.");
            return;
        }
        
        $this->log("Found " . count($issues) . " inconsistencies to fix.");
        
        foreach ($issues as $issue) {
            $this->fixInconsistency($issue);
        }
        
        $this->log("Consistency check completed.");
    }
    
    /**
     * 查找数据不一致的情况
     */
    private function findInconsistencies() {
        $issues = [];
        
        try {
            // 查找Proxmox服务与addon数据不匹配的情况
            $inconsistentServices = Capsule::table('tblhosting as h')
                ->join('tblproducts as p', 'h.packageid', '=', 'p.id')
                ->leftJoin('mg_proxmoxaddon_services as ps', 'h.id', '=', 'ps.service_id')
                ->where('p.servertype', 'proxmoxVPS')
                ->where(function($query) {
                    $query->whereNull('ps.service_id')
                          ->orWhere('ps.client_id', '!=', Capsule::raw('h.userid'));
                })
                ->select([
                    'h.id as service_id',
                    'h.userid as correct_client_id',
                    'ps.client_id as current_client_id',
                    'h.domain as service_domain'
                ])
                ->get();
            
            foreach ($inconsistentServices as $service) {
                $issues[] = [
                    'type' => 'service_mapping',
                    'service_id' => $service->service_id,
                    'correct_client_id' => $service->correct_client_id,
                    'current_client_id' => $service->current_client_id,
                    'service_domain' => $service->service_domain
                ];
            }
            
        } catch (Exception $e) {
            $this->log("Error finding inconsistencies: " . $e->getMessage());
        }
        
        return $issues;
    }
    
    /**
     * 修复单个不一致问题
     */
    private function fixInconsistency($issue) {
        try {
            $serviceId = $issue['service_id'];
            $correctClientId = $issue['correct_client_id'];
            $currentClientId = $issue['current_client_id'];
            
            $this->log("Fixing service #{$serviceId}: {$currentClientId} → {$correctClientId}");
            
            if ($this->dryRun) {
                $this->log("DRY RUN: Would fix service #{$serviceId}");
                return;
            }
            
            // 更新或插入服务映射
            Capsule::table('mg_proxmoxaddon_services')
                ->updateOrInsert(
                    ['service_id' => $serviceId],
                    ['client_id' => $correctClientId, 'updated_at' => date('Y-m-d H:i:s')]
                );
            
            // 确保用户映射存在
            $this->ensureUserMapping($correctClientId);
            
            // 更新相关表
            $this->updateRelatedTables($serviceId, $correctClientId);
            
            $this->log("Fixed service #{$serviceId} mapping");
            
        } catch (Exception $e) {
            $this->log("Error fixing service #{$serviceId}: " . $e->getMessage());
        }
    }
    
    /**
     * 确保用户映射存在
     */
    private function ensureUserMapping($clientId) {
        try {
            $exists = Capsule::table('mg_proxmoxaddon_users')
                ->where('client_id', $clientId)
                ->exists();
            
            if (!$exists) {
                Capsule::table('mg_proxmoxaddon_users')
                    ->insert([
                        'client_id' => $clientId,
                        'realm' => 'pve',
                        'pve_user' => 'vm' . $clientId,
                        'created_at' => date('Y-m-d H:i:s'),
                        'updated_at' => date('Y-m-d H:i:s')
                    ]);
                
                $this->log("Created user mapping for client #{$clientId}");
            }
        } catch (Exception $e) {
            $this->log("Error ensuring user mapping for client #{$clientId}: " . $e->getMessage());
        }
    }
    
    /**
     * 更新相关表
     */
    private function updateRelatedTables($serviceId, $clientId) {
        $tables = [
            'mg_proxmoxaddon_vms',
            'mg_proxmoxaddon_ips',
            'ProxmoxAddon_Backups'
        ];
        
        foreach ($tables as $table) {
            try {
                if ($this->tableExists($table)) {
                    $updated = Capsule::table($table)
                        ->where('service_id', $serviceId)
                        ->update(['client_id' => $clientId]);
                    
                    if ($updated > 0) {
                        $this->log("Updated {$updated} records in {$table}");
                    }
                }
            } catch (Exception $e) {
                $this->log("Error updating {$table}: " . $e->getMessage());
            }
        }
    }
    
    /**
     * 检查表是否存在
     */
    private function tableExists($tableName) {
        try {
            Capsule::schema()->hasTable($tableName);
            return true;
        } catch (Exception $e) {
            return false;
        }
    }
    
    /**
     * 记录日志
     */
    private function log($message) {
        $timestamp = date('Y-m-d H:i:s');
        $logMessage = "[{$timestamp}] {$this->logPrefix}: {$message}";
        
        // 写入WHMCS活动日志
        logActivity($logMessage);
        
        // 也输出到控制台（如果是命令行运行）
        if (php_sapi_name() === 'cli') {
            echo $logMessage . PHP_EOL;
        }
    }
    
    /**
     * 生成报告
     */
    public function generateReport() {
        $this->log("Generating Proxmox data consistency report...");
        
        $issues = $this->findInconsistencies();
        
        if (empty($issues)) {
            $this->log("Report: All Proxmox data is consistent.");
            return;
        }
        
        $this->log("Report: Found " . count($issues) . " inconsistencies:");
        
        foreach ($issues as $issue) {
            $this->log("  - Service #{$issue['service_id']} ({$issue['service_domain']}): " .
                      "Should be client #{$issue['correct_client_id']}, " .
                      "currently " . ($issue['current_client_id'] ?: 'unmapped'));
        }
    }
}

// 如果直接运行此脚本
if (php_sapi_name() === 'cli') {
    $dryRun = in_array('--dry-run', $argv);
    $reportOnly = in_array('--report', $argv);
    
    $checker = new ProxmoxTransferChecker($dryRun);
    
    if ($reportOnly) {
        $checker->generateReport();
    } else {
        $checker->checkAndFix();
    }
}
