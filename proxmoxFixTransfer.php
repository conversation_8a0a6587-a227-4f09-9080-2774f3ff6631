<?php
/**
 * Proxmox Addon 补丁 – 同步客户转移
 * 版本：WHMCS 8.x + Proxmox Addon 3.11+
 *
 * 修复WHMCS ownership transfer后Proxmox插件数据不同步的问题
 * 支持多种hook点以确保完整覆盖
 */

use WHMCS\Database\Capsule;

/**
 * 检查服务是否使用Proxmox模块
 */
function isProxmoxService($serviceId) {
    try {
        $service = Capsule::table('tblhosting')
            ->join('tblproducts', 'tblhosting.packageid', '=', 'tblproducts.id')
            ->where('tblhosting.id', $serviceId)
            ->where('tblproducts.servertype', 'proxmoxVPS')
            ->first(['tblhosting.id']);

        return !empty($service);
    } catch (Exception $e) {
        logActivity("ProxmoxFixTransfer Error: Failed to check service type - " . $e->getMessage());
        return false;
    }
}

/**
 * 同步Proxmox数据到新客户
 */
function syncProxmoxData($serviceId, $newClientId, $oldClientId = null) {
    try {
        // 检查是否为Proxmox服务
        if (!isProxmoxService($serviceId)) {
            return;
        }

        $logPrefix = "ProxmoxFixTransfer [Service #{$serviceId}]";

        // ① 更新服务映射表
        $serviceUpdated = Capsule::table('mg_proxmoxaddon_services')
            ->where('service_id', $serviceId)
            ->update(['client_id' => $newClientId]);

        // ② 检查并创建新客户的用户映射
        $existingUser = Capsule::table('mg_proxmoxaddon_users')
            ->where('client_id', $newClientId)
            ->first();

        if (!$existingUser) {
            Capsule::table('mg_proxmoxaddon_users')
                ->insert([
                    'client_id' => $newClientId,
                    'realm' => 'pve',
                    'pve_user' => 'vm' . $newClientId,
                    'created_at' => date('Y-m-d H:i:s'),
                    'updated_at' => date('Y-m-d H:i:s')
                ]);
            logActivity("{$logPrefix}: Created new user mapping for client #{$newClientId}");
        }

        // ③ 更新VM相关数据（如果存在）
        $vmUpdated = Capsule::table('mg_proxmoxaddon_vms')
            ->where('service_id', $serviceId)
            ->update(['client_id' => $newClientId]);

        // ④ 更新IP地址分配（如果存在）
        $ipUpdated = Capsule::table('mg_proxmoxaddon_ips')
            ->where('service_id', $serviceId)
            ->update(['client_id' => $newClientId]);

        // ⑤ 更新备份记录（如果存在）
        $backupUpdated = Capsule::table('ProxmoxAddon_Backups')
            ->where('service_id', $serviceId)
            ->update(['client_id' => $newClientId]);

        // ⑥ 清理缓存
        try {
            Capsule::table('ProxmoxAddon_ModuleCache')->truncate();
        } catch (Exception $e) {
            // 缓存表可能不存在，忽略错误
        }

        // ⑦ 记录同步结果
        $updates = [];
        if ($serviceUpdated) $updates[] = "services";
        if ($vmUpdated) $updates[] = "vms";
        if ($ipUpdated) $updates[] = "ips";
        if ($backupUpdated) $updates[] = "backups";

        $updateStr = empty($updates) ? "no updates needed" : "updated: " . implode(", ", $updates);
        logActivity("{$logPrefix}: Transfer sync completed - {$updateStr} → client #{$newClientId}");

    } catch (Exception $e) {
        logActivity("ProxmoxFixTransfer Error: " . $e->getMessage());
    }
}

/**
 * Hook 1: AdminServiceEdit - 服务编辑后触发
 */
add_hook('AdminServiceEdit', 1, function ($vars) {
    if (isset($vars['serviceid']) && isset($vars['userid'])) {
        syncProxmoxData((int)$vars['serviceid'], (int)$vars['userid']);
    }
});

/**
 * Hook 2: PreAdminServiceEdit - 服务编辑前触发，获取原客户ID
 */
add_hook('PreAdminServiceEdit', 1, function ($vars) {
    if (isset($vars['serviceid'])) {
        try {
            $service = Capsule::table('tblhosting')
                ->where('id', $vars['serviceid'])
                ->first(['userid']);

            if ($service) {
                // 将原客户ID存储到全局变量中
                $GLOBALS['proxmox_original_client_' . $vars['serviceid']] = $service->userid;
            }
        } catch (Exception $e) {
            logActivity("ProxmoxFixTransfer PreEdit Error: " . $e->getMessage());
        }
    }
});

/**
 * Hook 3: AdminAreaPage - 在管理员页面加载时检查
 * 这个hook可以捕获一些其他方式的服务转移
 */
add_hook('AdminAreaPage', 1, function ($vars) {
    // 只在服务管理页面执行
    if (isset($vars['filename']) && $vars['filename'] === 'clientsservices.php') {
        if (isset($_POST['userid']) && isset($_POST['id'])) {
            $serviceId = (int)$_POST['id'];
            $newClientId = (int)$_POST['userid'];

            // 检查是否是转移操作
            if (isset($GLOBALS['proxmox_original_client_' . $serviceId])) {
                $oldClientId = $GLOBALS['proxmox_original_client_' . $serviceId];
                if ($oldClientId != $newClientId) {
                    syncProxmoxData($serviceId, $newClientId, $oldClientId);
                }
            }
        }
    }
});