<?php
/**
 * Proxmox Addon 补丁 – 同步客户转移
 * 版本：WHMCS 8.x + Proxmox Addon 3.11+
 *
 * 修复WHMCS ownership transfer后Proxmox插件数据不同步的问题
 * 支持多种hook点以确保完整覆盖
 */

use WHMCS\Database\Capsule;

/**
 * 检查服务是否使用Proxmox模块
 */
function isProxmoxService($serviceId) {
    try {
        $service = Capsule::table('tblhosting')
            ->join('tblproducts', 'tblhosting.packageid', '=', 'tblproducts.id')
            ->where('tblhosting.id', $serviceId)
            ->where('tblproducts.servertype', 'proxmoxVPS')
            ->first(['tblhosting.id']);

        return !empty($service);
    } catch (Exception $e) {
        logActivity("ProxmoxFixTransfer Error: Failed to check service type - " . $e->getMessage());
        return false;
    }
}

/**
 * 同步Proxmox数据到新客户
 */
function syncProxmoxData($serviceId, $newClientId, $oldClientId = null) {
    try {
        // 检查是否为Proxmox服务
        if (!isProxmoxService($serviceId)) {
            return;
        }

        $logPrefix = "ProxmoxFixTransfer [Service #{$serviceId}]";

        // ① 更新服务映射表
        $serviceUpdated = Capsule::table('mg_proxmoxaddon_services')
            ->where('service_id', $serviceId)
            ->update(['client_id' => $newClientId]);

        // ② 检查并创建新客户的用户映射
        $existingUser = Capsule::table('mg_proxmoxaddon_users')
            ->where('client_id', $newClientId)
            ->first();

        if (!$existingUser) {
            Capsule::table('mg_proxmoxaddon_users')
                ->insert([
                    'client_id' => $newClientId,
                    'realm' => 'pve',
                    'pve_user' => 'vm' . $newClientId,
                    'created_at' => date('Y-m-d H:i:s'),
                    'updated_at' => date('Y-m-d H:i:s')
                ]);
            logActivity("{$logPrefix}: Created new user mapping for client #{$newClientId}");
        }

        // ③ 更新VM相关数据（如果存在）
        $vmUpdated = Capsule::table('mg_proxmoxaddon_vms')
            ->where('service_id', $serviceId)
            ->update(['client_id' => $newClientId]);

        // ④ 更新IP地址分配（如果存在）
        $ipUpdated = Capsule::table('mg_proxmoxaddon_ips')
            ->where('service_id', $serviceId)
            ->update(['client_id' => $newClientId]);

        // ⑤ 更新备份记录（如果存在）
        $backupUpdated = Capsule::table('ProxmoxAddon_Backups')
            ->where('service_id', $serviceId)
            ->update(['client_id' => $newClientId]);

        // ⑥ 清理缓存
        try {
            Capsule::table('ProxmoxAddon_ModuleCache')->truncate();
        } catch (Exception $e) {
            // 缓存表可能不存在，忽略错误
        }

        // ⑦ 记录同步结果
        $updates = [];
        if ($serviceUpdated) $updates[] = "services";
        if ($vmUpdated) $updates[] = "vms";
        if ($ipUpdated) $updates[] = "ips";
        if ($backupUpdated) $updates[] = "backups";

        $updateStr = empty($updates) ? "no updates needed" : "updated: " . implode(", ", $updates);
        logActivity("{$logPrefix}: Transfer sync completed - {$updateStr} → client #{$newClientId}");

    } catch (Exception $e) {
        logActivity("ProxmoxFixTransfer Error: " . $e->getMessage());
    }
}

/**
 * Hook 1: AdminServiceEdit - 服务编辑后触发
 */
add_hook('AdminServiceEdit', 1, function ($vars) {
    if (isset($vars['serviceid']) && isset($vars['userid'])) {
        syncProxmoxData((int)$vars['serviceid'], (int)$vars['userid']);
    }
});

/**
 * Hook 2: PreAdminServiceEdit - 服务编辑前触发，获取原客户ID
 */
add_hook('PreAdminServiceEdit', 1, function ($vars) {
    if (isset($vars['serviceid'])) {
        try {
            $service = Capsule::table('tblhosting')
                ->where('id', $vars['serviceid'])
                ->first(['userid']);

            if ($service) {
                // 将原客户ID存储到全局变量中
                $GLOBALS['proxmox_original_client_' . $vars['serviceid']] = $service->userid;
            }
        } catch (Exception $e) {
            logActivity("ProxmoxFixTransfer PreEdit Error: " . $e->getMessage());
        }
    }
});

/**
 * Hook 3: AdminAreaPage - 在管理员页面加载时检查
 * 这个hook可以捕获一些其他方式的服务转移
 */
add_hook('AdminAreaPage', 1, function ($vars) {
    // 在多个可能的页面执行检查
    $checkPages = ['clientsservices.php', 'clientssummary.php', 'clientsproducts.php'];

    if (isset($vars['filename']) && in_array($vars['filename'], $checkPages)) {
        // 检查POST数据
        if (isset($_POST['userid']) && isset($_POST['id'])) {
            $serviceId = (int)$_POST['id'];
            $newClientId = (int)$_POST['userid'];

            // 检查是否是转移操作
            if (isset($GLOBALS['proxmox_original_client_' . $serviceId])) {
                $oldClientId = $GLOBALS['proxmox_original_client_' . $serviceId];
                if ($oldClientId != $newClientId) {
                    syncProxmoxData($serviceId, $newClientId, $oldClientId);
                }
            }
        }

        // 额外检查：如果是服务页面，检查是否有不一致的数据
        if ($vars['filename'] === 'clientsservices.php' && isset($_GET['id'])) {
            checkServiceConsistency((int)$_GET['id']);
        }
    }
});

/**
 * 检查单个服务的数据一致性
 */
function checkServiceConsistency($serviceId) {
    try {
        if (!isProxmoxService($serviceId)) {
            return;
        }

        // 获取WHMCS中的客户ID
        $service = Capsule::table('tblhosting')
            ->where('id', $serviceId)
            ->first(['userid']);

        if (!$service) {
            return;
        }

        // 检查Proxmox插件中的客户ID
        $proxmoxService = Capsule::table('mg_proxmoxaddon_services')
            ->where('service_id', $serviceId)
            ->first(['client_id']);

        // 如果不一致，自动修复
        if (!$proxmoxService || $proxmoxService->client_id != $service->userid) {
            logActivity("ProxmoxFixTransfer: Detected inconsistency for service #{$serviceId}, auto-fixing...");
            syncProxmoxData($serviceId, $service->userid);
        }

    } catch (Exception $e) {
        logActivity("ProxmoxFixTransfer Consistency Check Error: " . $e->getMessage());
    }
}

/**
 * Hook 4: 定期检查 - 通过cron触发
 * 这个hook会在每次cron运行时检查数据一致性
 */
add_hook('DailyCronJob', 1, function ($vars) {
    checkAllProxmoxServices();
});

/**
 * 检查所有Proxmox服务的数据一致性
 */
function checkAllProxmoxServices() {
    try {
        logActivity("ProxmoxFixTransfer: Starting daily consistency check...");

        $inconsistentServices = Capsule::table('tblhosting as h')
            ->join('tblproducts as p', 'h.packageid', '=', 'p.id')
            ->leftJoin('mg_proxmoxaddon_services as ps', 'h.id', '=', 'ps.service_id')
            ->where('p.servertype', 'proxmoxVPS')
            ->where('h.domainstatus', 'Active') // 只检查活跃服务
            ->where(function($query) {
                $query->whereNull('ps.service_id')
                      ->orWhere('ps.client_id', '!=', Capsule::raw('h.userid'));
            })
            ->select(['h.id as service_id', 'h.userid as correct_client_id', 'h.domain'])
            ->get();

        $fixedCount = 0;
        foreach ($inconsistentServices as $service) {
            syncProxmoxData($service->service_id, $service->correct_client_id);
            $fixedCount++;
        }

        if ($fixedCount > 0) {
            logActivity("ProxmoxFixTransfer: Daily check completed - fixed {$fixedCount} inconsistencies");
        } else {
            logActivity("ProxmoxFixTransfer: Daily check completed - no issues found");
        }

    } catch (Exception $e) {
        logActivity("ProxmoxFixTransfer Daily Check Error: " . $e->getMessage());
    }
}