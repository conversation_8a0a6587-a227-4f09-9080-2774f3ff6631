<?php
/**
 * Proxmox Addon 补丁 – 同步客户转移
 * 版本：WHMCS 8.x + Proxmox Addon 3.11
 */

use WHMCS\Database\Capsule;

add_hook('AdminServiceEdit', 1, function ($vars) {

    $serviceId   = (int) $vars['serviceid'];   // 服务 ID
    $newClientId = (int) $vars['userid'];      // 转移后的 Client ID

    // ① 更新服务映射
    Capsule::table('mg_proxmoxaddon_services')
        ->where('service_id', $serviceId)
        ->update(['client_id' => $newClientId]);

    // ② 若用户映射不存在就创建
    Capsule::table('mg_proxmoxaddon_users')
        ->updateOrInsert(
            ['client_id' => $newClientId],
            ['realm' => 'pve', 'pve_user' => 'vm' . $newClientId]
        );

    // ③ 清缓存避免 Duplicate entry
    Capsule::table('ProxmoxAddon_ModuleCache')->truncate();

    logActivity("ProxmoxFixTransfer: svc #{$serviceId} → client #{$newClientId} synced");
});