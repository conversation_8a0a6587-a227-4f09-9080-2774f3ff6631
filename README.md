# Proxmox WHMCS Ownership Transfer 修复方案

## 问题描述

使用WHMCS自带的ownership transfer功能转移Proxmox VPS产品后，新用户无法正常进行重装系统等操作，出现以下错误：

```
No query results for model [ModulesGarden\ProxmoxAddon\App\Models\User]
```

## 原因分析

WHMCS的ownership transfer功能只更新核心的服务表（`tblhosting`），但不会自动更新第三方模块的数据库表。ModulesGarden的Proxmox插件使用独立的数据库表来管理用户和服务映射关系，导致数据不同步。

## 解决方案

由于ModulesGarden的Proxmox插件明确表示WHMCS没有对应的hook支持ownership transfer，我们提供了一个多层次的解决方案：

### 1. 增强Hook方案（实时修复）

**文件：** `proxmoxFixTransfer.php`

这个hook会在多个关键点检查和修复数据不一致。

#### 安装步骤：

1. 将 `proxmoxFixTransfer.php` 上传到WHMCS的 `includes/hooks/` 目录
2. 确保文件权限正确（644）
3. Hook会自动生效，无需额外配置

#### 功能特性：

- ✅ 多个hook点覆盖（AdminServiceEdit, PreAdminServiceEdit, AdminAreaPage, DailyCronJob）
- ✅ 自动检测Proxmox服务
- ✅ 实时一致性检查
- ✅ 同步所有相关数据库表
- ✅ 完整的错误处理和日志记录

### 2. 自动检测器（智能修复）

**文件：** `proxmox_auto_detector.php`

这个hook会在用户访问Proxmox服务时自动检查和修复数据不一致。

#### 安装步骤：

1. 将 `proxmox_auto_detector.php` 上传到WHMCS的 `includes/hooks/` 目录
2. 确保文件权限正确（644）

#### 功能特性：

- ✅ 客户区域访问时自动检查
- ✅ 管理员查看服务时自动检查
- ✅ 模块操作前自动检查
- ✅ 每日批量一致性检查
- ✅ 零配置自动运行

### 3. 手动修复工具（Web界面）

**文件：** `proxmox_manual_fix.php`

提供Web界面进行手动检查和修复。

#### 安装步骤：

1. 将 `proxmox_manual_fix.php` 上传到WHMCS根目录
2. 在浏览器中访问：`https://your-whmcs.com/proxmox_manual_fix.php`
3. 使用管理员账号登录后即可使用

#### 功能特性：

- ✅ Web界面操作
- ✅ 单个服务修复
- ✅ 批量检查和修复
- ✅ 实时状态显示
- ✅ 安全的管理员权限验证

### 4. 定期检查方案（命令行工具）

**文件：** `proxmoxTransferChecker.php`

命令行工具，可以通过cron定期运行。

#### 安装步骤：

1. 将 `proxmoxTransferChecker.php` 上传到WHMCS根目录
2. 设置cron job（可选，因为自动检测器已包含每日检查）：
   ```bash
   0 * * * * php -q /path/to/whmcs/proxmoxTransferChecker.php
   ```

#### 使用方法：

```bash
# 正常运行（修复问题）
php proxmoxTransferChecker.php

# 测试运行（不修改数据）
php proxmoxTransferChecker.php --dry-run

# 仅生成报告
php proxmoxTransferChecker.php --report
```

## 数据库表说明

本方案会同步以下Proxmox插件相关的数据库表：

| 表名 | 用途 | 关键字段 |
|------|------|----------|
| `mg_proxmoxaddon_services` | 服务映射 | `service_id`, `client_id` |
| `mg_proxmoxaddon_users` | 用户映射 | `client_id`, `pve_user` |
| `mg_proxmoxaddon_vms` | VM信息 | `service_id`, `client_id` |
| `mg_proxmoxaddon_ips` | IP分配 | `service_id`, `client_id` |
| `ProxmoxAddon_Backups` | 备份记录 | `service_id`, `client_id` |

## 推荐部署方案

### 🚀 快速解决方案（推荐）

1. **立即部署自动检测器**：
   ```bash
   # 上传到 includes/hooks/ 目录
   cp proxmox_auto_detector.php /path/to/whmcs/includes/hooks/
   ```

2. **部署手动修复工具**：
   ```bash
   # 上传到WHMCS根目录
   cp proxmox_manual_fix.php /path/to/whmcs/
   ```

3. **立即修复现有问题**：
   - 访问 `https://your-whmcs.com/proxmox_manual_fix.php`
   - 点击 "Check All Services" 查看问题
   - 点击 "Fix All Services" 修复所有问题

### 🔧 完整部署方案

如果需要最大覆盖，可以同时部署所有组件：

```bash
# 部署所有hook文件
cp proxmoxFixTransfer.php /path/to/whmcs/includes/hooks/
cp proxmox_auto_detector.php /path/to/whmcs/includes/hooks/

# 部署工具
cp proxmox_manual_fix.php /path/to/whmcs/
cp proxmoxTransferChecker.php /path/to/whmcs/
```

## 测试验证

### 1. 转移前检查

```sql
-- 查看转移前的数据
SELECT h.id, h.userid, h.domain, ps.client_id
FROM tblhosting h
LEFT JOIN mg_proxmoxaddon_services ps ON h.id = ps.service_id
WHERE h.id = [SERVICE_ID];
```

### 2. 执行转移

使用WHMCS管理员界面进行ownership transfer

### 3. 自动修复验证

转移后，系统会在以下时机自动检查和修复：
- ✅ 新客户首次访问服务时
- ✅ 管理员查看服务时
- ✅ 执行任何模块操作时
- ✅ 每日定时检查时

### 4. 手动验证

```sql
-- 验证数据是否同步
SELECT h.id, h.userid, h.domain, ps.client_id
FROM tblhosting h
LEFT JOIN mg_proxmoxaddon_services ps ON h.id = ps.service_id
WHERE h.id = [SERVICE_ID];

-- 检查用户映射
SELECT * FROM mg_proxmoxaddon_users WHERE client_id = [NEW_CLIENT_ID];
```

### 5. 功能测试

在客户区域测试以下功能：
- ✅ 重装系统
- ✅ 重启/关机
- ✅ 控制台访问
- ✅ 备份管理

## 日志监控

所有操作都会记录到WHMCS活动日志中，搜索关键词：
- `ProxmoxFixTransfer`
- `ProxmoxTransferChecker`

## 故障排除

### 常见问题

1. **Hook不生效**
   - 检查文件路径：`includes/hooks/proxmoxFixTransfer.php`
   - 检查文件权限和语法错误
   - 查看WHMCS错误日志

2. **数据库连接错误**
   - 确认WHMCS数据库配置正确
   - 检查数据库用户权限

3. **表不存在错误**
   - 确认Proxmox插件已正确安装
   - 检查数据库表前缀设置

### 手动修复

如果自动修复失败，可以手动执行SQL：

```sql
-- 更新服务映射
UPDATE mg_proxmoxaddon_services 
SET client_id = [NEW_CLIENT_ID] 
WHERE service_id = [SERVICE_ID];

-- 创建用户映射（如果不存在）
INSERT IGNORE INTO mg_proxmoxaddon_users 
(client_id, realm, pve_user, created_at, updated_at) 
VALUES ([NEW_CLIENT_ID], 'pve', 'vm[NEW_CLIENT_ID]', NOW(), NOW());

-- 更新其他相关表
UPDATE mg_proxmoxaddon_vms SET client_id = [NEW_CLIENT_ID] WHERE service_id = [SERVICE_ID];
UPDATE mg_proxmoxaddon_ips SET client_id = [NEW_CLIENT_ID] WHERE service_id = [SERVICE_ID];
UPDATE ProxmoxAddon_Backups SET client_id = [NEW_CLIENT_ID] WHERE service_id = [SERVICE_ID];
```

## 兼容性

- ✅ WHMCS 8.x
- ✅ ModulesGarden Proxmox VE VPS 3.11+
- ✅ PHP 8.1+
- ✅ MySQL 5.7+

## 安全注意事项

1. 在生产环境部署前，请先在测试环境验证
2. 建议在执行前备份数据库
3. 定期检查日志确保正常运行
4. 使用 `--dry-run` 参数测试脚本功能

## 支持

如果遇到问题，请提供以下信息：
- WHMCS版本
- Proxmox插件版本
- 错误日志
- 数据库表结构（如有变化）

---

**注意：** 本方案基于ModulesGarden Proxmox VE VPS插件的标准数据库结构。如果您的插件版本或配置有特殊修改，可能需要相应调整。
