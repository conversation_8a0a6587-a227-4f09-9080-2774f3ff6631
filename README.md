# Proxmox WHMCS Ownership Transfer 修复方案

## 问题描述

使用WHMCS自带的ownership transfer功能转移Proxmox VPS产品后，新用户无法正常进行重装系统等操作，出现以下错误：

```
No query results for model [ModulesGarden\ProxmoxAddon\App\Models\User]
```

## 原因分析

WHMCS的ownership transfer功能只更新核心的服务表（`tblhosting`），但不会自动更新第三方模块的数据库表。ModulesGarden的Proxmox插件使用独立的数据库表来管理用户和服务映射关系，导致数据不同步。

## 解决方案

本方案提供了两个互补的解决方法：

### 1. Hook方案（实时修复）

**文件：** `proxmoxFixTransfer.php`

这个hook会在服务转移时自动同步Proxmox插件的数据库表。

#### 安装步骤：

1. 将 `proxmoxFixTransfer.php` 上传到WHMCS的 `includes/hooks/` 目录
2. 确保文件权限正确（644）
3. Hook会自动生效，无需额外配置

#### 功能特性：

- ✅ 支持多个hook点，确保完整覆盖
- ✅ 自动检测Proxmox服务
- ✅ 同步所有相关数据库表
- ✅ 完整的错误处理和日志记录
- ✅ 自动创建缺失的用户映射

### 2. 定期检查方案（批量修复）

**文件：** `proxmoxTransferChecker.php`

这个脚本可以定期检查和修复数据不一致的问题。

#### 安装步骤：

1. 将 `proxmoxTransferChecker.php` 上传到WHMCS根目录
2. 设置cron job（建议每小时运行一次）：
   ```bash
   0 * * * * php -q /path/to/whmcs/proxmoxTransferChecker.php
   ```

#### 使用方法：

```bash
# 正常运行（修复问题）
php proxmoxTransferChecker.php

# 测试运行（不修改数据）
php proxmoxTransferChecker.php --dry-run

# 仅生成报告
php proxmoxTransferChecker.php --report
```

## 数据库表说明

本方案会同步以下Proxmox插件相关的数据库表：

| 表名 | 用途 | 关键字段 |
|------|------|----------|
| `mg_proxmoxaddon_services` | 服务映射 | `service_id`, `client_id` |
| `mg_proxmoxaddon_users` | 用户映射 | `client_id`, `pve_user` |
| `mg_proxmoxaddon_vms` | VM信息 | `service_id`, `client_id` |
| `mg_proxmoxaddon_ips` | IP分配 | `service_id`, `client_id` |
| `ProxmoxAddon_Backups` | 备份记录 | `service_id`, `client_id` |

## 测试验证

### 1. 转移前检查

```sql
-- 查看转移前的数据
SELECT h.id, h.userid, h.domain, ps.client_id 
FROM tblhosting h 
LEFT JOIN mg_proxmoxaddon_services ps ON h.id = ps.service_id 
WHERE h.id = [SERVICE_ID];
```

### 2. 执行转移

使用WHMCS管理员界面进行ownership transfer

### 3. 转移后验证

```sql
-- 验证数据是否同步
SELECT h.id, h.userid, h.domain, ps.client_id 
FROM tblhosting h 
LEFT JOIN mg_proxmoxaddon_services ps ON h.id = ps.service_id 
WHERE h.id = [SERVICE_ID];

-- 检查用户映射
SELECT * FROM mg_proxmoxaddon_users WHERE client_id = [NEW_CLIENT_ID];
```

### 4. 功能测试

在客户区域测试以下功能：
- ✅ 重装系统
- ✅ 重启/关机
- ✅ 控制台访问
- ✅ 备份管理

## 日志监控

所有操作都会记录到WHMCS活动日志中，搜索关键词：
- `ProxmoxFixTransfer`
- `ProxmoxTransferChecker`

## 故障排除

### 常见问题

1. **Hook不生效**
   - 检查文件路径：`includes/hooks/proxmoxFixTransfer.php`
   - 检查文件权限和语法错误
   - 查看WHMCS错误日志

2. **数据库连接错误**
   - 确认WHMCS数据库配置正确
   - 检查数据库用户权限

3. **表不存在错误**
   - 确认Proxmox插件已正确安装
   - 检查数据库表前缀设置

### 手动修复

如果自动修复失败，可以手动执行SQL：

```sql
-- 更新服务映射
UPDATE mg_proxmoxaddon_services 
SET client_id = [NEW_CLIENT_ID] 
WHERE service_id = [SERVICE_ID];

-- 创建用户映射（如果不存在）
INSERT IGNORE INTO mg_proxmoxaddon_users 
(client_id, realm, pve_user, created_at, updated_at) 
VALUES ([NEW_CLIENT_ID], 'pve', 'vm[NEW_CLIENT_ID]', NOW(), NOW());

-- 更新其他相关表
UPDATE mg_proxmoxaddon_vms SET client_id = [NEW_CLIENT_ID] WHERE service_id = [SERVICE_ID];
UPDATE mg_proxmoxaddon_ips SET client_id = [NEW_CLIENT_ID] WHERE service_id = [SERVICE_ID];
UPDATE ProxmoxAddon_Backups SET client_id = [NEW_CLIENT_ID] WHERE service_id = [SERVICE_ID];
```

## 兼容性

- ✅ WHMCS 8.x
- ✅ ModulesGarden Proxmox VE VPS 3.11+
- ✅ PHP 8.1+
- ✅ MySQL 5.7+

## 安全注意事项

1. 在生产环境部署前，请先在测试环境验证
2. 建议在执行前备份数据库
3. 定期检查日志确保正常运行
4. 使用 `--dry-run` 参数测试脚本功能

## 支持

如果遇到问题，请提供以下信息：
- WHMCS版本
- Proxmox插件版本
- 错误日志
- 数据库表结构（如有变化）

---

**注意：** 本方案基于ModulesGarden Proxmox VE VPS插件的标准数据库结构。如果您的插件版本或配置有特殊修改，可能需要相应调整。
