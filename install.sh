#!/bin/bash

# Proxmox WHMCS Transfer Fix - 安装脚本
# 使用方法: chmod +x install.sh && ./install.sh

echo "==================================="
echo "Proxmox WHMCS Transfer Fix Installer"
echo "==================================="

# 检查是否在WHMCS目录中
if [ ! -f "configuration.php" ]; then
    echo "❌ 错误: 请在WHMCS根目录中运行此脚本"
    echo "   当前目录应包含 configuration.php 文件"
    exit 1
fi

echo "✅ 检测到WHMCS安装目录"

# 创建hooks目录（如果不存在）
if [ ! -d "includes/hooks" ]; then
    echo "📁 创建 includes/hooks 目录..."
    mkdir -p includes/hooks
fi

# 安装文件
echo "📦 安装修复文件..."

# 复制hook文件
if [ -f "proxmoxFixTransfer.php" ]; then
    cp proxmoxFixTransfer.php includes/hooks/
    echo "✅ 已安装: proxmoxFixTransfer.php → includes/hooks/"
else
    echo "⚠️  警告: proxmoxFixTransfer.php 文件未找到"
fi

if [ -f "proxmox_auto_detector.php" ]; then
    cp proxmox_auto_detector.php includes/hooks/
    echo "✅ 已安装: proxmox_auto_detector.php → includes/hooks/"
else
    echo "⚠️  警告: proxmox_auto_detector.php 文件未找到"
fi

# 复制工具文件
if [ -f "proxmox_manual_fix.php" ]; then
    cp proxmox_manual_fix.php ./
    echo "✅ 已安装: proxmox_manual_fix.php → WHMCS根目录"
else
    echo "⚠️  警告: proxmox_manual_fix.php 文件未找到"
fi

if [ -f "proxmoxTransferChecker.php" ]; then
    cp proxmoxTransferChecker.php ./
    echo "✅ 已安装: proxmoxTransferChecker.php → WHMCS根目录"
else
    echo "⚠️  警告: proxmoxTransferChecker.php 文件未找到"
fi

# 设置文件权限
echo "🔐 设置文件权限..."
chmod 644 includes/hooks/proxmoxFixTransfer.php 2>/dev/null
chmod 644 includes/hooks/proxmox_auto_detector.php 2>/dev/null
chmod 644 proxmox_manual_fix.php 2>/dev/null
chmod 755 proxmoxTransferChecker.php 2>/dev/null

echo ""
echo "🎉 安装完成！"
echo ""
echo "📋 下一步操作："
echo "1. 访问手动修复工具检查现有问题:"
echo "   https://your-domain.com/proxmox_manual_fix.php"
echo ""
echo "2. 点击 'Check All Services' 查看不一致的服务"
echo "3. 点击 'Fix All Services' 修复所有问题"
echo ""
echo "4. 系统现在会自动检测和修复未来的转移问题"
echo ""
echo "📊 监控日志:"
echo "   在WHMCS管理员界面 → Utilities → Logs → Activity Log"
echo "   搜索关键词: ProxmoxFixTransfer, ProxmoxAutoDetector"
echo ""
echo "⚠️  重要提醒:"
echo "   - 建议先在测试环境验证"
echo "   - 执行修复前请备份数据库"
echo "   - 如有问题请查看WHMCS错误日志"
echo ""
echo "==================================="
