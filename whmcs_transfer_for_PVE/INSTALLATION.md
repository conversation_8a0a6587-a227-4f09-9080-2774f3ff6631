# PVE Transfer Module - Installation Guide

## 📋 系统要求

- WHMCS 8.x 或更高版本
- PHP 8.1 或更高版本
- ModulesGarden Proxmox VE VPS 3.11+ 
- MySQL 5.7 或更高版本
- 启用的 PHP 扩展：PDO, JSON, OpenSSL

## 🚀 安装步骤

### 1. 上传文件

将整个 `modules` 文件夹上传到您的 WHMCS 根目录，确保文件结构如下：

```
/path/to/whmcs/
├── modules/
│   └── addons/
│       └── pve_transfer/
│           ├── pve_transfer.php
│           ├── hooks.php
│           ├── lib/
│           │   ├── TransferManager.php
│           │   ├── PVEIntegration.php
│           │   ├── ClientController.php
│           │   ├── AdminController.php
│           │   ├── Logger.php
│           │   └── NotificationService.php
│           └── templates/
│               ├── dashboard.tpl
│               ├── create.tpl
│               ├── error.tpl
│               └── success.tpl
```

### 2. 设置文件权限

```bash
chmod 644 modules/addons/pve_transfer/pve_transfer.php
chmod 644 modules/addons/pve_transfer/hooks.php
chmod -R 644 modules/addons/pve_transfer/lib/
chmod -R 644 modules/addons/pve_transfer/templates/
```

### 3. 激活模块

1. 登录 WHMCS 管理员界面
2. 导航到 **Setup → Addon Modules**
3. 找到 "PVE Transfer" 模块
4. 点击 **Activate**
5. 配置模块设置（见下方配置部分）

### 4. 配置模块

激活后，配置以下设置：

#### 基本设置
- **Enable Module**: 启用模块功能
- **Allowed Client Groups**: 允许使用转移功能的客户组ID（逗号分隔，留空表示所有用户）
- **Require Admin Approval**: 是否需要管理员审批转移请求
- **Email Notifications**: 是否发送邮件通知

#### 高级设置
- **Transfer Limit (Hours)**: 同一服务两次转移之间的最小间隔时间
- **Auto Expire (Hours)**: 转移请求自动过期时间
- **Debug Mode**: 启用调试日志记录

### 5. 验证安装

1. **检查数据库表**：
   确认以下表已创建：
   - `mod_pve_transfer_requests`
   - `mod_pve_transfer_history`
   - `mod_pve_transfer_config`

2. **测试客户区域**：
   - 登录客户区域
   - 访问 `index.php?m=pve_transfer`
   - 确认页面正常显示

3. **测试管理员界面**：
   - 在管理员界面访问 **Addon Modules → PVE Transfer**
   - 确认管理面板正常显示

## 🔧 配置选项详解

### 客户组限制
如果您想限制只有特定客户组可以使用转移功能：

1. 在 **Setup → Client Groups** 中查看客户组ID
2. 在模块配置中输入允许的组ID，例如：`1,2,3`
3. 留空表示允许所有客户使用

### 邮件模板
模块会自动发送以下类型的邮件：
- 转移请求创建通知
- 转移完成通知
- 转移拒绝通知
- 转移过期通知

您可以在 **Setup → Email Templates** 中自定义这些模板。

### 权限设置
确保以下权限正确设置：
- 客户可以查看自己的服务
- 客户可以访问addon模块页面
- 管理员可以管理转移请求

## 🔍 故障排除

### 常见问题

1. **模块不显示在客户区域**
   - 检查模块是否已激活
   - 确认客户有可转移的Proxmox服务
   - 检查客户组权限设置

2. **数据库错误**
   - 确认数据库用户有CREATE TABLE权限
   - 检查WHMCS数据库连接配置
   - 查看错误日志获取详细信息

3. **邮件不发送**
   - 检查WHMCS邮件配置
   - 确认邮件模板存在
   - 启用调试模式查看详细日志

4. **PVE数据不同步**
   - 确认ModulesGarden Proxmox插件已安装
   - 检查相关数据库表是否存在
   - 查看活动日志中的同步记录

### 日志检查

启用调试模式后，可以在以下位置查看日志：
- **WHMCS活动日志**：Utilities → Logs → Activity Log
- **PHP错误日志**：服务器错误日志文件
- **搜索关键词**：`PVETransfer`

### 数据库检查

如果遇到问题，可以手动检查数据库：

```sql
-- 检查模块表是否存在
SHOW TABLES LIKE 'mod_pve_transfer%';

-- 检查转移请求
SELECT * FROM mod_pve_transfer_requests ORDER BY created_at DESC LIMIT 10;

-- 检查转移历史
SELECT * FROM mod_pve_transfer_history ORDER BY completed_at DESC LIMIT 10;
```

## 🔄 升级指南

### 从旧版本升级

1. **备份数据库**：
   ```bash
   mysqldump -u username -p database_name > backup.sql
   ```

2. **备份现有文件**：
   ```bash
   cp -r modules/addons/pve_transfer modules/addons/pve_transfer.backup
   ```

3. **上传新文件**：
   覆盖现有文件

4. **运行升级**：
   在管理员界面重新激活模块，系统会自动运行升级脚本

### 版本兼容性

- v1.0.x → v1.1.x：自动升级，无需手动操作
- 跨大版本升级：请查看具体版本的升级说明

## 🛡️ 安全建议

1. **定期备份**：
   - 定期备份数据库
   - 备份模块文件

2. **权限控制**：
   - 合理设置客户组权限
   - 启用管理员审批（如需要）

3. **监控日志**：
   - 定期检查活动日志
   - 监控异常转移活动

4. **更新维护**：
   - 保持WHMCS和模块更新
   - 定期清理过期数据

## 📞 技术支持

如果在安装过程中遇到问题：

1. **检查系统要求**：确认所有要求都已满足
2. **查看日志**：启用调试模式并查看详细日志
3. **联系支持**：提供详细的错误信息和系统环境

---

**安装完成后，您就可以开始使用专业的PVE产品转移功能了！**
