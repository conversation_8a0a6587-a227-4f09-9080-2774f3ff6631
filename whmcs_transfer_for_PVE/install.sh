#!/bin/bash

# PVE Transfer Module - Quick Installation Script
# 使用方法: chmod +x install.sh && ./install.sh

echo "=========================================="
echo "PVE Transfer Module - Quick Installer"
echo "=========================================="

# 检查是否在WHMCS目录中
if [ ! -f "configuration.php" ]; then
    echo "❌ 错误: 请在WHMCS根目录中运行此脚本"
    echo "   当前目录应包含 configuration.php 文件"
    exit 1
fi

echo "✅ 检测到WHMCS安装目录"

# 检查PHP版本
PHP_VERSION=$(php -r "echo PHP_VERSION;" 2>/dev/null)
if [ $? -ne 0 ]; then
    echo "❌ 错误: 未找到PHP或PHP不可执行"
    exit 1
fi

echo "✅ PHP版本: $PHP_VERSION"

# 检查必需的PHP扩展
echo "🔍 检查PHP扩展..."
REQUIRED_EXTENSIONS=("pdo" "json" "openssl")
for ext in "${REQUIRED_EXTENSIONS[@]}"; do
    if php -m | grep -q "^$ext$"; then
        echo "  ✅ $ext"
    else
        echo "  ❌ $ext (缺失)"
        MISSING_EXTENSIONS=true
    fi
done

if [ "$MISSING_EXTENSIONS" = true ]; then
    echo "❌ 错误: 缺少必需的PHP扩展"
    exit 1
fi

# 创建目标目录
echo "📁 创建模块目录..."
TARGET_DIR="modules/addons/pve_transfer"

if [ ! -d "modules/addons" ]; then
    mkdir -p modules/addons
    echo "  ✅ 创建 modules/addons 目录"
fi

if [ -d "$TARGET_DIR" ]; then
    echo "⚠️  目标目录已存在，创建备份..."
    BACKUP_DIR="${TARGET_DIR}.backup.$(date +%Y%m%d_%H%M%S)"
    mv "$TARGET_DIR" "$BACKUP_DIR"
    echo "  ✅ 备份到: $BACKUP_DIR"
fi

mkdir -p "$TARGET_DIR"
mkdir -p "$TARGET_DIR/lib"
mkdir -p "$TARGET_DIR/templates"

# 复制文件
echo "📦 安装模块文件..."

# 检查源文件是否存在
SOURCE_DIR="modules/addons/pve_transfer"
if [ ! -f "$SOURCE_DIR/pve_transfer.php" ]; then
    echo "❌ 错误: 源文件不存在，请确认文件结构正确"
    echo "   期望的文件: $SOURCE_DIR/pve_transfer.php"
    exit 1
fi

# 复制主文件
cp "$SOURCE_DIR/pve_transfer.php" "$TARGET_DIR/"
cp "$SOURCE_DIR/hooks.php" "$TARGET_DIR/"

# 复制库文件
cp -r "$SOURCE_DIR/lib/"* "$TARGET_DIR/lib/"

# 复制模板文件
cp -r "$SOURCE_DIR/templates/"* "$TARGET_DIR/templates/"

echo "✅ 文件复制完成"

# 设置权限
echo "🔐 设置文件权限..."
chmod 644 "$TARGET_DIR/pve_transfer.php"
chmod 644 "$TARGET_DIR/hooks.php"
chmod -R 644 "$TARGET_DIR/lib/"
chmod -R 644 "$TARGET_DIR/templates/"

echo "✅ 权限设置完成"

# 检查数据库连接
echo "🔍 检查数据库连接..."
DB_TEST=$(php -r "
require_once 'init.php';
try {
    \$pdo = \WHMCS\Database\Capsule::connection()->getPdo();
    echo 'success';
} catch (Exception \$e) {
    echo 'error: ' . \$e->getMessage();
}
" 2>/dev/null)

if [[ $DB_TEST == "success" ]]; then
    echo "✅ 数据库连接正常"
else
    echo "❌ 数据库连接失败: $DB_TEST"
    echo "   请检查WHMCS数据库配置"
    exit 1
fi

# 检查Proxmox插件
echo "🔍 检查Proxmox插件..."
PROXMOX_CHECK=$(php -r "
require_once 'init.php';
try {
    \$tables = \WHMCS\Database\Capsule::select('SHOW TABLES LIKE \"mg_proxmoxaddon_%\"');
    if (count(\$tables) > 0) {
        echo 'found';
    } else {
        echo 'not_found';
    }
} catch (Exception \$e) {
    echo 'error';
}
" 2>/dev/null)

if [[ $PROXMOX_CHECK == "found" ]]; then
    echo "✅ 检测到Proxmox插件"
elif [[ $PROXMOX_CHECK == "not_found" ]]; then
    echo "⚠️  警告: 未检测到Proxmox插件相关表"
    echo "   请确认ModulesGarden Proxmox插件已正确安装"
else
    echo "❌ 检查Proxmox插件时出错"
fi

# 生成配置建议
echo ""
echo "🎉 安装完成！"
echo ""
echo "📋 下一步操作："
echo "1. 登录WHMCS管理员界面"
echo "2. 导航到 Setup → Addon Modules"
echo "3. 找到 'PVE Transfer' 模块并点击 Activate"
echo "4. 配置模块设置："
echo "   - Enable Module: Yes"
echo "   - Email Notifications: Yes"
echo "   - Transfer Limit: 24 (小时)"
echo "   - Auto Expire: 72 (小时)"
echo ""
echo "🔧 推荐配置："
echo "   - Allowed Client Groups: 留空（允许所有用户）"
echo "   - Require Admin Approval: No（自动处理）"
echo "   - Debug Mode: No（生产环境）"
echo ""
echo "🔍 验证安装："
echo "1. 客户区域测试: https://your-domain.com/index.php?m=pve_transfer"
echo "2. 管理员界面: Setup → Addon Modules → PVE Transfer"
echo ""
echo "📊 监控日志:"
echo "   在WHMCS管理员界面 → Utilities → Logs → Activity Log"
echo "   搜索关键词: PVETransfer"
echo ""
echo "⚠️  重要提醒:"
echo "   - 建议先在测试环境验证功能"
echo "   - 生产环境使用前请备份数据库"
echo "   - 如有问题请查看WHMCS错误日志"
echo ""
echo "=========================================="

# 检查是否需要重启Web服务器
echo "💡 提示: 如果使用OPcache，可能需要重启Web服务器或清理缓存"
echo ""

exit 0
