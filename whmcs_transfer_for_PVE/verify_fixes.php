<?php
/**
 * PVE Transfer Module - 修复验证脚本
 * 用于验证所有修复是否正确应用
 */

echo "========================================\n";
echo "PVE Transfer Module - Fix Verification\n";
echo "========================================\n\n";

$errors = [];
$warnings = [];
$success = [];

// 检查主模块文件
echo "1. 检查主模块文件...\n";
$mainFile = 'modules/addons/pve_transfer/pve_transfer.php';
if (file_exists($mainFile)) {
    $content = file_get_contents($mainFile);
    
    // 检查是否有重复的函数定义
    $activateCount = substr_count($content, 'function pve_transfer_activate()');
    $deactivateCount = substr_count($content, 'function pve_transfer_deactivate()');
    
    if ($activateCount > 1) {
        $errors[] = "主模块文件中仍有重复的 pve_transfer_activate() 函数定义 ({$activateCount}个)";
    } else {
        $success[] = "✅ 主模块文件中没有重复的函数定义";
    }
    
    // 检查PHP语法
    $syntaxCheck = shell_exec("php -l {$mainFile} 2>&1");
    if (strpos($syntaxCheck, 'No syntax errors') !== false) {
        $success[] = "✅ 主模块文件PHP语法正确";
    } else {
        $errors[] = "主模块文件PHP语法错误: " . trim($syntaxCheck);
    }
} else {
    $errors[] = "主模块文件不存在: {$mainFile}";
}

// 检查钩子文件
echo "2. 检查钩子文件...\n";
$hookFile = 'includes/hooks/pve_transfer.php';
if (file_exists($hookFile)) {
    $content = file_get_contents($hookFile);
    
    // 检查是否使用了错误的方法
    if (strpos($content, 'isLoggedIn()') !== false) {
        $errors[] = "钩子文件中仍在使用错误的 isLoggedIn() 方法";
    } else {
        $success[] = "✅ 钩子文件中没有使用错误的 isLoggedIn() 方法";
    }
    
    if (strpos($content, 'isAuthenticatedClient()') !== false) {
        $errors[] = "钩子文件中仍在使用错误的 isAuthenticatedClient() 方法";
    } else {
        $success[] = "✅ 钩子文件中没有使用错误的 isAuthenticatedClient() 方法";
    }
    
    // 检查是否使用了正确的方法
    if (strpos($content, '$client = $currentUser->client()') !== false) {
        $success[] = "✅ 钩子文件中使用了正确的 client() 方法";
    } else {
        $warnings[] = "钩子文件中可能没有使用正确的 client() 方法";
    }
    
    // 检查PHP语法
    $syntaxCheck = shell_exec("php -l {$hookFile} 2>&1");
    if (strpos($syntaxCheck, 'No syntax errors') !== false) {
        $success[] = "✅ 钩子文件PHP语法正确";
    } else {
        $errors[] = "钩子文件PHP语法错误: " . trim($syntaxCheck);
    }
} else {
    $errors[] = "钩子文件不存在: {$hookFile}";
}

// 检查ClientController文件
echo "3. 检查ClientController文件...\n";
$controllerFile = 'modules/addons/pve_transfer/lib/ClientController.php';
if (file_exists($controllerFile)) {
    $content = file_get_contents($controllerFile);
    
    // 检查是否使用了错误的方法
    if (strpos($content, 'isLoggedIn()') !== false) {
        $errors[] = "ClientController文件中仍在使用错误的 isLoggedIn() 方法";
    } else {
        $success[] = "✅ ClientController文件中没有使用错误的 isLoggedIn() 方法";
    }
    
    // 检查是否使用了正确的方法
    if (strpos($content, '$client = $this->currentUser->client()') !== false) {
        $success[] = "✅ ClientController文件中使用了正确的 client() 方法";
    } else {
        $warnings[] = "ClientController文件中可能没有使用正确的 client() 方法";
    }
    
    // 检查PHP语法
    $syntaxCheck = shell_exec("php -l {$controllerFile} 2>&1");
    if (strpos($syntaxCheck, 'No syntax errors') !== false) {
        $success[] = "✅ ClientController文件PHP语法正确";
    } else {
        $errors[] = "ClientController文件PHP语法错误: " . trim($syntaxCheck);
    }
} else {
    $errors[] = "ClientController文件不存在: {$controllerFile}";
}

// 检查其他必要文件
echo "4. 检查其他必要文件...\n";
$requiredFiles = [
    'modules/addons/pve_transfer/lib/TransferManager.php',
    'modules/addons/pve_transfer/lib/AdminController.php',
    'modules/addons/pve_transfer/lib/PVEIntegration.php',
    'modules/addons/pve_transfer/templates/client_area.tpl',
    'modules/addons/pve_transfer/templates/admin_area.tpl'
];

foreach ($requiredFiles as $file) {
    if (file_exists($file)) {
        $success[] = "✅ 文件存在: " . basename($file);
        
        // 检查PHP文件的语法
        if (pathinfo($file, PATHINFO_EXTENSION) === 'php') {
            $syntaxCheck = shell_exec("php -l {$file} 2>&1");
            if (strpos($syntaxCheck, 'No syntax errors') === false) {
                $errors[] = "文件语法错误 {$file}: " . trim($syntaxCheck);
            }
        }
    } else {
        $warnings[] = "文件不存在: {$file}";
    }
}

// 输出结果
echo "\n========================================\n";
echo "验证结果\n";
echo "========================================\n\n";

if (!empty($success)) {
    echo "✅ 成功项目:\n";
    foreach ($success as $item) {
        echo "   {$item}\n";
    }
    echo "\n";
}

if (!empty($warnings)) {
    echo "⚠️  警告项目:\n";
    foreach ($warnings as $item) {
        echo "   {$item}\n";
    }
    echo "\n";
}

if (!empty($errors)) {
    echo "❌ 错误项目:\n";
    foreach ($errors as $item) {
        echo "   {$item}\n";
    }
    echo "\n";
    echo "请修复上述错误后重新运行验证。\n";
    exit(1);
} else {
    echo "🎉 所有检查通过！模块应该可以正常工作了。\n\n";
    echo "下一步:\n";
    echo "1. 在WHMCS管理员界面访问 Setup → Addon Modules\n";
    echo "2. 找到 'PVE Transfer' 模块并激活\n";
    echo "3. 测试访问: https://your-domain.com/index.php?m=pve_transfer\n";
    exit(0);
}
