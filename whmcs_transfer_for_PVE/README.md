# WHMCS Transfer for PVE

## 🚀 专业的产品转移插件

一个专为WHMCS和Proxmox VE插件设计的完美产品转移解决方案，让用户可以安全、便捷地转移自己的产品给其他用户。

## ✨ 核心特性

### 🔐 安全可靠
- **双重验证**：发送方和接收方都需要确认
- **邮件验证**：自动发送确认邮件
- **权限控制**：管理员可控制哪些用户组可以使用转移功能
- **审计日志**：完整记录所有转移操作

### 🎯 专为PVE优化
- **完美兼容**：专门为ModulesGarden Proxmox插件设计
- **数据同步**：自动同步所有相关数据库表
- **零停机**：转移过程中服务不中断
- **智能检测**：自动识别Proxmox服务

### 💼 用户友好
- **简洁界面**：直观的客户区域界面
- **实时状态**：转移进度实时显示
- **多语言支持**：支持中文、英文等多种语言
- **移动适配**：完美支持移动设备

### 🛠 管理功能
- **转移历史**：完整的转移记录
- **批量操作**：管理员可批量处理转移请求
- **统计报表**：转移数据统计分析
- **配置灵活**：丰富的配置选项

## 📋 功能列表

### 客户区域功能
- ✅ 发起产品转移请求
- ✅ 查看待处理的转移请求
- ✅ 接受/拒绝转移请求
- ✅ 转移历史记录
- ✅ 实时状态更新

### 管理员功能
- ✅ 转移请求管理
- ✅ 用户权限控制
- ✅ 转移历史查看
- ✅ 统计报表
- ✅ 系统配置

### 自动化功能
- ✅ 邮件通知
- ✅ 数据同步
- ✅ 状态更新
- ✅ 日志记录

## 🏗 技术架构

### 数据库设计
```sql
-- 转移请求表
mod_pve_transfer_requests
-- 转移历史表  
mod_pve_transfer_history
-- 配置表
mod_pve_transfer_config
```

### 核心组件
- **TransferManager**: 转移逻辑管理
- **PVEIntegration**: PVE插件集成
- **NotificationService**: 通知服务
- **SecurityValidator**: 安全验证

## 📦 安装要求

- WHMCS 8.x+
- PHP 8.1+
- ModulesGarden Proxmox VE VPS 3.11+
- MySQL 5.7+

## 🚀 快速安装

1. 上传模块文件到 `modules/addons/pve_transfer/`
2. 在WHMCS管理员界面激活模块
3. 配置模块设置
4. 开始使用！

## 🔧 配置选项

- **允许的用户组**: 控制哪些用户组可以使用转移功能
- **需要管理员审批**: 是否需要管理员审批转移请求
- **邮件通知**: 配置邮件通知设置
- **转移限制**: 设置转移频率限制

## 📖 使用说明

### 发起转移
1. 登录客户区域
2. 进入"产品转移"页面
3. 选择要转移的产品
4. 输入接收方邮箱
5. 确认转移请求

### 接受转移
1. 接收方收到邮件通知
2. 点击邮件中的链接
3. 登录并确认接受
4. 系统自动完成转移

## 🛡 安全特性

- **CSRF保护**: 防止跨站请求伪造
- **权限验证**: 严格的权限检查
- **数据加密**: 敏感数据加密存储
- **操作日志**: 完整的操作审计

## 🌍 多语言支持

- 🇨🇳 简体中文
- 🇺🇸 English
- 🇪🇸 Español
- 🇫🇷 Français

## 📞 技术支持

如有问题或建议，请联系：
- 邮箱: <EMAIL>
- 文档: https://docs.example.com
- 社区: https://community.example.com

## 📄 许可证

MIT License - 详见 LICENSE 文件

## 🏗 项目结构

```
whmcs_transfer_for_PVE/
├── README.md                           # 项目说明文档
├── INSTALLATION.md                     # 详细安装指南
├── install.sh                         # 快速安装脚本
├── includes/
│   └── hooks/
│       └── pve_transfer.php           # WHMCS钩子文件
└── modules/
    └── addons/
        └── pve_transfer/
            ├── pve_transfer.php        # 主模块文件
            ├── lib/                   # 核心库文件
            │   ├── TransferManager.php     # 转移管理器
            │   ├── PVEIntegration.php      # PVE插件集成
            │   ├── ClientController.php    # 客户区域控制器
            │   ├── AdminController.php     # 管理员控制器
            │   ├── Logger.php             # 日志记录器
            │   └── NotificationService.php # 通知服务
            └── templates/             # 模板文件
                ├── dashboard.tpl          # 主面板模板
                ├── create.tpl            # 创建转移模板
                ├── error.tpl             # 错误页面模板
                └── success.tpl           # 成功页面模板
```

## 🚀 快速开始

### 方法一：使用安装脚本（推荐）

```bash
# 1. 进入WHMCS根目录
cd /path/to/your/whmcs

# 2. 上传并解压项目文件
# 确保 whmcs_transfer_for_PVE 文件夹在WHMCS根目录

# 3. 运行安装脚本
chmod +x whmcs_transfer_for_PVE/install.sh
./whmcs_transfer_for_PVE/install.sh

# 4. 按照脚本提示完成配置
```

### 方法二：手动安装

1. **上传文件**：
   ```bash
   cp -r whmcs_transfer_for_PVE/modules/* /path/to/whmcs/modules/
   ```

2. **设置权限**：
   ```bash
   chmod -R 644 /path/to/whmcs/modules/addons/pve_transfer/
   ```

3. **激活模块**：
   - 登录WHMCS管理员界面
   - 导航到 Setup → Addon Modules
   - 激活 "PVE Transfer" 模块

## 💡 核心特性详解

### 🔐 安全机制
- **双重验证**：发送方创建请求，接收方邮件确认
- **令牌验证**：每个转移请求都有唯一的验证令牌
- **权限控制**：基于用户组的访问控制
- **审计追踪**：完整的操作日志记录

### 🎯 PVE深度集成
- **智能识别**：自动识别Proxmox VPS服务
- **完整同步**：同步所有相关数据库表
  - `mg_proxmoxaddon_services` - 服务映射
  - `mg_proxmoxaddon_users` - 用户映射
  - `mg_proxmoxaddon_vms` - VM信息
  - `mg_proxmoxaddon_ips` - IP分配
  - `ProxmoxAddon_Backups` - 备份记录
- **缓存清理**：自动清理模块缓存
- **数据验证**：转移前后数据一致性检查

### 📧 智能通知系统
- **创建通知**：向接收方发送转移请求邮件
- **完成通知**：向双方发送转移完成确认
- **拒绝通知**：通知发送方转移被拒绝
- **过期提醒**：自动处理过期的转移请求

### 🛠 管理功能
- **实时统计**：转移成功率、待处理数量等
- **批量操作**：管理员可批量处理转移请求
- **历史记录**：完整的转移历史追踪
- **自动清理**：定期清理过期请求

## 🔧 高级配置

### 客户组权限控制
```php
// 在模块配置中设置允许的客户组ID
'allowed_groups' => '1,2,3'  // 只允许组1,2,3的用户
'allowed_groups' => ''       // 允许所有用户
```

### 转移限制设置
```php
'transfer_limit_hours' => 24    // 24小时内同一服务只能转移一次
'auto_expire_hours' => 72       // 72小时后转移请求自动过期
```

### 邮件模板自定义
在WHMCS的邮件模板中可以自定义以下模板：
- `transfer_request_created` - 转移请求创建
- `transfer_completed_sender` - 转移完成（发送方）
- `transfer_completed_receiver` - 转移完成（接收方）
- `transfer_rejected` - 转移被拒绝
- `transfer_expired` - 转移过期

## 🔍 API接口

### 客户区域API
```php
// 创建转移请求
POST index.php?m=pve_transfer&action=create
{
    "service_id": 123,
    "to_email": "<EMAIL>",
    "message": "转移说明"
}

// 接受转移
POST index.php?m=pve_transfer&action=accept
{
    "request_id": 456,
    "token": "verification_token"
}
```

### 管理员API
```php
// 获取转移统计
GET addonmodules.php?module=pve_transfer&action=statistics

// 批量清理过期请求
POST addonmodules.php?module=pve_transfer&action=cleanup
```

## 🧪 测试指南

### 功能测试清单
- [ ] 创建转移请求
- [ ] 邮件通知发送
- [ ] 接受转移流程
- [ ] 拒绝转移流程
- [ ] 数据同步验证
- [ ] 权限控制测试
- [ ] 过期处理测试

### 测试环境搭建
1. 准备两个测试账户
2. 创建测试Proxmox VPS服务
3. 配置邮件发送（可使用MailHog等工具）
4. 启用调试模式监控日志

## 🐛 故障排除

### 常见问题及解决方案

**问题1：模块激活失败**
```bash
# 检查文件权限
ls -la modules/addons/pve_transfer/
# 检查PHP错误日志
tail -f /var/log/php_errors.log
```

**问题2：邮件不发送**
```php
// 检查WHMCS邮件配置
// 启用调试模式查看详细日志
'debug_mode' => true
```

**问题3：PVE数据不同步**
```sql
-- 检查PVE插件表
SHOW TABLES LIKE 'mg_proxmoxaddon%';
-- 验证数据一致性
SELECT h.userid, ps.client_id FROM tblhosting h
LEFT JOIN mg_proxmoxaddon_services ps ON h.id = ps.service_id
WHERE h.id = [SERVICE_ID];
```

## 📈 性能优化

### 数据库优化
```sql
-- 添加索引优化查询性能
ALTER TABLE mod_pve_transfer_requests ADD INDEX idx_status_expires (status, expires_at);
ALTER TABLE mod_pve_transfer_history ADD INDEX idx_completed_at (completed_at);
```

### 缓存策略
- 启用OPcache提升PHP性能
- 使用Redis缓存频繁查询的数据
- 定期清理过期的转移记录

## 🔄 版本更新

### 更新流程
1. 备份现有文件和数据库
2. 上传新版本文件
3. 在管理员界面重新激活模块
4. 检查更新日志确认新功能

### 版本兼容性
- v1.0.x：初始版本
- v1.1.x：增加批量操作功能
- v1.2.x：优化邮件模板系统

## 🤝 贡献指南

欢迎提交问题报告和功能建议！

### 开发环境设置
1. Fork项目仓库
2. 创建功能分支
3. 提交代码并创建Pull Request

### 代码规范
- 遵循PSR-4自动加载标准
- 使用有意义的变量和函数名
- 添加适当的注释和文档

---

**让产品转移变得简单、安全、高效！**

🌟 **如果这个项目对您有帮助，请给我们一个Star！**
