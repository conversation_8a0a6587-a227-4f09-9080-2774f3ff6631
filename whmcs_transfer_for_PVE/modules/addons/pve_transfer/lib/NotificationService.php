<?php

namespace PVETransfer;

use WHMCS\Database\Capsule;

/**
 * 通知服务类
 */
class NotificationService
{
    private $config;
    private $logger;

    public function __construct($config = [])
    {
        $this->config = $config;
        $this->logger = new Logger($config['debug_mode'] ?? false);
    }

    /**
     * 发送转移相关邮件
     */
    public function sendTransferEmail($requestId, $type)
    {
        try {
            $request = $this->getTransferRequestData($requestId);
            if (!$request) {
                throw new Exception('Transfer request not found');
            }

            switch ($type) {
                case 'created':
                    return $this->sendTransferCreatedEmail($request);
                case 'completed':
                    return $this->sendTransferCompletedEmail($request);
                case 'rejected':
                    return $this->sendTransferRejectedEmail($request);
                case 'expired':
                    return $this->sendTransferExpiredEmail($request);
                default:
                    throw new Exception('Unknown email type: ' . $type);
            }

        } catch (Exception $e) {
            $this->logger->error("Failed to send {$type} email for request {$requestId}: " . $e->getMessage());
            return false;
        }
    }

    /**
     * 发送转移请求创建邮件
     */
    private function sendTransferCreatedEmail($request)
    {
        $verifyUrl = $this->generateVerificationUrl($request);
        
        $emailData = [
            'to' => $request->to_email,
            'subject' => 'Product Transfer Request - ' . $request->service_domain,
            'template' => 'transfer_request_created',
            'vars' => [
                'from_name' => $request->from_firstname . ' ' . $request->from_lastname,
                'from_email' => $request->from_email,
                'service_domain' => $request->service_domain,
                'product_name' => $request->product_name,
                'message' => $request->message,
                'verify_url' => $verifyUrl,
                'expires_at' => date('Y-m-d H:i:s', strtotime($request->expires_at)),
            ]
        ];

        return $this->sendEmail($emailData);
    }

    /**
     * 发送转移完成邮件
     */
    private function sendTransferCompletedEmail($request)
    {
        // 发送给发送方
        $senderEmailData = [
            'to' => $request->from_email,
            'subject' => 'Product Transfer Completed - ' . $request->service_domain,
            'template' => 'transfer_completed_sender',
            'vars' => [
                'to_name' => $request->to_firstname . ' ' . $request->to_lastname,
                'to_email' => $request->to_email,
                'service_domain' => $request->service_domain,
                'product_name' => $request->product_name,
            ]
        ];

        // 发送给接收方
        $receiverEmailData = [
            'to' => $request->to_email,
            'subject' => 'Product Transfer Received - ' . $request->service_domain,
            'template' => 'transfer_completed_receiver',
            'vars' => [
                'from_name' => $request->from_firstname . ' ' . $request->from_lastname,
                'service_domain' => $request->service_domain,
                'product_name' => $request->product_name,
            ]
        ];

        $result1 = $this->sendEmail($senderEmailData);
        $result2 = $this->sendEmail($receiverEmailData);

        return $result1 && $result2;
    }

    /**
     * 发送转移拒绝邮件
     */
    private function sendTransferRejectedEmail($request)
    {
        $emailData = [
            'to' => $request->from_email,
            'subject' => 'Product Transfer Rejected - ' . $request->service_domain,
            'template' => 'transfer_rejected',
            'vars' => [
                'to_name' => $request->to_firstname . ' ' . $request->to_lastname,
                'service_domain' => $request->service_domain,
                'product_name' => $request->product_name,
                'reason' => $request->message,
            ]
        ];

        return $this->sendEmail($emailData);
    }

    /**
     * 发送转移过期邮件
     */
    private function sendTransferExpiredEmail($request)
    {
        $emailData = [
            'to' => $request->from_email,
            'subject' => 'Product Transfer Expired - ' . $request->service_domain,
            'template' => 'transfer_expired',
            'vars' => [
                'service_domain' => $request->service_domain,
                'product_name' => $request->product_name,
                'to_email' => $request->to_email,
            ]
        ];

        return $this->sendEmail($emailData);
    }

    /**
     * 获取转移请求数据
     */
    private function getTransferRequestData($requestId)
    {
        return Capsule::table('mod_pve_transfer_requests as r')
            ->join('tblhosting as h', 'r.service_id', '=', 'h.id')
            ->join('tblproducts as p', 'h.packageid', '=', 'p.id')
            ->join('tblclients as from_client', 'r.from_client_id', '=', 'from_client.id')
            ->leftJoin('tblclients as to_client', 'r.to_client_id', '=', 'to_client.id')
            ->where('r.id', $requestId)
            ->select([
                'r.*',
                'h.domain as service_domain',
                'p.name as product_name',
                'from_client.firstname as from_firstname',
                'from_client.lastname as from_lastname',
                'from_client.email as from_email',
                'to_client.firstname as to_firstname',
                'to_client.lastname as to_lastname'
            ])
            ->first();
    }

    /**
     * 生成验证URL
     */
    private function generateVerificationUrl($request)
    {
        $baseUrl = rtrim($_SERVER['HTTP_HOST'], '/');
        $protocol = (!empty($_SERVER['HTTPS']) && $_SERVER['HTTPS'] !== 'off') ? 'https://' : 'http://';
        
        return $protocol . $baseUrl . '/index.php?m=pve_transfer&action=verify&request_id=' . 
               $request->id . '&token=' . $request->verification_token;
    }

    /**
     * 发送邮件
     */
    private function sendEmail($emailData)
    {
        try {
            // 使用WHMCS的邮件系统
            if (function_exists('sendMessage')) {
                $result = sendMessage(
                    $emailData['template'],
                    $emailData['vars'],
                    $emailData['to']
                );
                
                $this->logger->log("Email sent: {$emailData['template']} to {$emailData['to']}");
                return true;
            } else {
                // 备用邮件发送方法
                return $this->sendFallbackEmail($emailData);
            }

        } catch (Exception $e) {
            $this->logger->error("Failed to send email: " . $e->getMessage());
            return false;
        }
    }

    /**
     * 备用邮件发送方法
     */
    private function sendFallbackEmail($emailData)
    {
        try {
            $headers = [
                'From: ' . ($_SERVER['SERVER_NAME'] ?? '<EMAIL>'),
                'Reply-To: ' . ($_SERVER['SERVER_NAME'] ?? '<EMAIL>'),
                'Content-Type: text/html; charset=UTF-8'
            ];

            $body = $this->generateEmailBody($emailData['template'], $emailData['vars']);

            return mail(
                $emailData['to'],
                $emailData['subject'],
                $body,
                implode("\r\n", $headers)
            );

        } catch (Exception $e) {
            $this->logger->error("Fallback email failed: " . $e->getMessage());
            return false;
        }
    }

    /**
     * 生成邮件内容
     */
    private function generateEmailBody($template, $vars)
    {
        // 简单的邮件模板生成
        switch ($template) {
            case 'transfer_request_created':
                return "
                <h2>Product Transfer Request</h2>
                <p>Hello,</p>
                <p>{$vars['from_name']} ({$vars['from_email']}) has requested to transfer the following service to you:</p>
                <ul>
                    <li><strong>Service:</strong> {$vars['service_domain']}</li>
                    <li><strong>Product:</strong> {$vars['product_name']}</li>
                </ul>
                " . (!empty($vars['message']) ? "<p><strong>Message:</strong> {$vars['message']}</p>" : "") . "
                <p>To accept this transfer, please click the link below:</p>
                <p><a href=\"{$vars['verify_url']}\">Accept Transfer</a></p>
                <p>This request will expire on {$vars['expires_at']}.</p>
                ";

            case 'transfer_completed_sender':
                return "
                <h2>Transfer Completed</h2>
                <p>Your transfer request has been completed successfully.</p>
                <p>Service {$vars['service_domain']} has been transferred to {$vars['to_name']} ({$vars['to_email']}).</p>
                ";

            case 'transfer_completed_receiver':
                return "
                <h2>Transfer Received</h2>
                <p>You have successfully received a service transfer from {$vars['from_name']}.</p>
                <p>Service {$vars['service_domain']} is now in your account.</p>
                ";

            case 'transfer_rejected':
                return "
                <h2>Transfer Rejected</h2>
                <p>Your transfer request for {$vars['service_domain']} has been rejected by {$vars['to_name']}.</p>
                " . (!empty($vars['reason']) ? "<p><strong>Reason:</strong> {$vars['reason']}</p>" : "") . "
                ";

            case 'transfer_expired':
                return "
                <h2>Transfer Expired</h2>
                <p>Your transfer request for {$vars['service_domain']} to {$vars['to_email']} has expired.</p>
                <p>You can create a new transfer request if needed.</p>
                ";

            default:
                return "<p>Transfer notification</p>";
        }
    }
}
