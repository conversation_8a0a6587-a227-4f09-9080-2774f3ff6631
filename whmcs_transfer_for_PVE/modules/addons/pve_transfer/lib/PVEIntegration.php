<?php

namespace PVETransfer;

use WHMCS\Database\Capsule;
use Exception;

/**
 * PVE插件集成类
 * 专门处理与ModulesGarden Proxmox插件的数据同步
 */
class PVEIntegration
{
    private $logger;

    public function __construct()
    {
        $this->logger = new Logger();
    }

    /**
     * 同步转移数据到PVE插件
     */
    public function syncTransfer($serviceId, $newClientId)
    {
        try {
            $this->logger->log("Starting PVE sync for service {$serviceId} to client {$newClientId}");

            // 1. 更新服务映射表
            $this->updateServiceMapping($serviceId, $newClientId);

            // 2. 确保用户映射存在
            $this->ensureUserMapping($newClientId);

            // 3. 更新VM相关数据
            $this->updateVMData($serviceId, $newClientId);

            // 4. 更新IP地址分配
            $this->updateIPAssignments($serviceId, $newClientId);

            // 5. 更新备份记录
            $this->updateBackupRecords($serviceId, $newClientId);

            // 6. 更新快照记录
            $this->updateSnapshotRecords($serviceId, $newClientId);

            // 7. 清理缓存
            $this->clearModuleCache();

            $this->logger->log("PVE sync completed successfully for service {$serviceId}");

            return [
                'success' => true,
                'message' => 'PVE data synchronized successfully'
            ];

        } catch (Exception $e) {
            $this->logger->log("PVE sync failed: " . $e->getMessage());
            return [
                'success' => false,
                'message' => $e->getMessage()
            ];
        }
    }

    /**
     * 更新服务映射表
     */
    private function updateServiceMapping($serviceId, $newClientId)
    {
        try {
            // 检查表是否存在
            if (!$this->tableExists('mg_proxmoxaddon_services')) {
                $this->logger->log("Warning: mg_proxmoxaddon_services table not found");
                return;
            }

            // 更新或插入服务映射
            $updated = Capsule::table('mg_proxmoxaddon_services')
                ->updateOrInsert(
                    ['service_id' => $serviceId],
                    [
                        'client_id' => $newClientId,
                        'updated_at' => date('Y-m-d H:i:s')
                    ]
                );

            $this->logger->log("Service mapping updated for service {$serviceId}");

        } catch (Exception $e) {
            throw new Exception("Failed to update service mapping: " . $e->getMessage());
        }
    }

    /**
     * 确保用户映射存在
     */
    private function ensureUserMapping($clientId)
    {
        try {
            // 检查表是否存在
            if (!$this->tableExists('mg_proxmoxaddon_users')) {
                $this->logger->log("Warning: mg_proxmoxaddon_users table not found");
                return;
            }

            // 检查用户映射是否已存在
            $existingUser = Capsule::table('mg_proxmoxaddon_users')
                ->where('client_id', $clientId)
                ->first();

            if (!$existingUser) {
                // 创建新的用户映射
                Capsule::table('mg_proxmoxaddon_users')
                    ->insert([
                        'client_id' => $clientId,
                        'realm' => 'pve',
                        'pve_user' => 'vm' . $clientId,
                        'created_at' => date('Y-m-d H:i:s'),
                        'updated_at' => date('Y-m-d H:i:s')
                    ]);

                $this->logger->log("Created user mapping for client {$clientId}");
            } else {
                $this->logger->log("User mapping already exists for client {$clientId}");
            }

        } catch (Exception $e) {
            throw new Exception("Failed to ensure user mapping: " . $e->getMessage());
        }
    }

    /**
     * 更新VM相关数据
     */
    private function updateVMData($serviceId, $newClientId)
    {
        try {
            $tables = ['mg_proxmoxaddon_vms', 'mg_proxmoxaddon_vm_configs'];

            foreach ($tables as $table) {
                if ($this->tableExists($table)) {
                    $updated = Capsule::table($table)
                        ->where('service_id', $serviceId)
                        ->update([
                            'client_id' => $newClientId,
                            'updated_at' => date('Y-m-d H:i:s')
                        ]);

                    if ($updated > 0) {
                        $this->logger->log("Updated {$updated} records in {$table}");
                    }
                }
            }

        } catch (Exception $e) {
            throw new Exception("Failed to update VM data: " . $e->getMessage());
        }
    }

    /**
     * 更新IP地址分配
     */
    private function updateIPAssignments($serviceId, $newClientId)
    {
        try {
            $tables = ['mg_proxmoxaddon_ips', 'mg_proxmoxaddon_ip_assignments'];

            foreach ($tables as $table) {
                if ($this->tableExists($table)) {
                    $updated = Capsule::table($table)
                        ->where('service_id', $serviceId)
                        ->update([
                            'client_id' => $newClientId,
                            'updated_at' => date('Y-m-d H:i:s')
                        ]);

                    if ($updated > 0) {
                        $this->logger->log("Updated {$updated} IP records in {$table}");
                    }
                }
            }

        } catch (Exception $e) {
            throw new Exception("Failed to update IP assignments: " . $e->getMessage());
        }
    }

    /**
     * 更新备份记录
     */
    private function updateBackupRecords($serviceId, $newClientId)
    {
        try {
            $tables = ['ProxmoxAddon_Backups', 'mg_proxmoxaddon_backups'];

            foreach ($tables as $table) {
                if ($this->tableExists($table)) {
                    $updated = Capsule::table($table)
                        ->where('service_id', $serviceId)
                        ->update([
                            'client_id' => $newClientId,
                            'updated_at' => date('Y-m-d H:i:s')
                        ]);

                    if ($updated > 0) {
                        $this->logger->log("Updated {$updated} backup records in {$table}");
                    }
                }
            }

        } catch (Exception $e) {
            throw new Exception("Failed to update backup records: " . $e->getMessage());
        }
    }

    /**
     * 更新快照记录
     */
    private function updateSnapshotRecords($serviceId, $newClientId)
    {
        try {
            $tables = ['mg_proxmoxaddon_snapshots', 'ProxmoxAddon_Snapshots'];

            foreach ($tables as $table) {
                if ($this->tableExists($table)) {
                    $updated = Capsule::table($table)
                        ->where('service_id', $serviceId)
                        ->update([
                            'client_id' => $newClientId,
                            'updated_at' => date('Y-m-d H:i:s')
                        ]);

                    if ($updated > 0) {
                        $this->logger->log("Updated {$updated} snapshot records in {$table}");
                    }
                }
            }

        } catch (Exception $e) {
            throw new Exception("Failed to update snapshot records: " . $e->getMessage());
        }
    }

    /**
     * 清理模块缓存
     */
    private function clearModuleCache()
    {
        try {
            $cacheTables = ['ProxmoxAddon_ModuleCache', 'mg_proxmoxaddon_cache'];

            foreach ($cacheTables as $table) {
                if ($this->tableExists($table)) {
                    Capsule::table($table)->truncate();
                    $this->logger->log("Cleared cache table: {$table}");
                }
            }

        } catch (Exception $e) {
            // 缓存清理失败不应该阻止转移过程
            $this->logger->log("Warning: Failed to clear cache: " . $e->getMessage());
        }
    }

    /**
     * 检查表是否存在
     */
    private function tableExists($tableName)
    {
        try {
            return Capsule::schema()->hasTable($tableName);
        } catch (Exception $e) {
            $this->logger->log("Error checking table existence for {$tableName}: " . $e->getMessage());
            return false;
        }
    }

    /**
     * 获取服务的PVE相关信息
     */
    public function getServicePVEInfo($serviceId)
    {
        try {
            $info = [];

            // 获取服务映射信息
            if ($this->tableExists('mg_proxmoxaddon_services')) {
                $serviceMapping = Capsule::table('mg_proxmoxaddon_services')
                    ->where('service_id', $serviceId)
                    ->first();
                $info['service_mapping'] = $serviceMapping;
            }

            // 获取VM信息
            if ($this->tableExists('mg_proxmoxaddon_vms')) {
                $vmInfo = Capsule::table('mg_proxmoxaddon_vms')
                    ->where('service_id', $serviceId)
                    ->first();
                $info['vm_info'] = $vmInfo;
            }

            // 获取IP分配信息
            if ($this->tableExists('mg_proxmoxaddon_ips')) {
                $ipAssignments = Capsule::table('mg_proxmoxaddon_ips')
                    ->where('service_id', $serviceId)
                    ->get();
                $info['ip_assignments'] = $ipAssignments;
            }

            return $info;

        } catch (Exception $e) {
            $this->logger->log("Failed to get PVE info for service {$serviceId}: " . $e->getMessage());
            return [];
        }
    }

    /**
     * 验证PVE数据一致性
     */
    public function validateDataConsistency($serviceId)
    {
        try {
            $issues = [];

            // 获取WHMCS服务信息
            $service = Capsule::table('tblhosting')
                ->where('id', $serviceId)
                ->first();

            if (!$service) {
                return ['valid' => false, 'issues' => ['Service not found']];
            }

            // 检查服务映射
            if ($this->tableExists('mg_proxmoxaddon_services')) {
                $serviceMapping = Capsule::table('mg_proxmoxaddon_services')
                    ->where('service_id', $serviceId)
                    ->first();

                if (!$serviceMapping) {
                    $issues[] = 'Service mapping not found in PVE addon';
                } elseif ($serviceMapping->client_id != $service->userid) {
                    $issues[] = 'Service mapping client_id mismatch';
                }
            }

            // 检查用户映射
            if ($this->tableExists('mg_proxmoxaddon_users')) {
                $userMapping = Capsule::table('mg_proxmoxaddon_users')
                    ->where('client_id', $service->userid)
                    ->first();

                if (!$userMapping) {
                    $issues[] = 'User mapping not found in PVE addon';
                }
            }

            return [
                'valid' => empty($issues),
                'issues' => $issues
            ];

        } catch (Exception $e) {
            return [
                'valid' => false,
                'issues' => ['Validation error: ' . $e->getMessage()]
            ];
        }
    }

    /**
     * 修复数据不一致问题
     */
    public function fixDataInconsistency($serviceId)
    {
        try {
            $service = Capsule::table('tblhosting')
                ->where('id', $serviceId)
                ->first();

            if (!$service) {
                throw new Exception('Service not found');
            }

            // 使用现有的同步方法修复数据
            return $this->syncTransfer($serviceId, $service->userid);

        } catch (Exception $e) {
            return [
                'success' => false,
                'message' => 'Failed to fix inconsistency: ' . $e->getMessage()
            ];
        }
    }
}
