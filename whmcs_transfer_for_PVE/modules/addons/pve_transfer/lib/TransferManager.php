<?php

namespace PVETransfer;

use WHMCS\Database\Capsule;
use Exception;

/**
 * 转移管理器 - 核心业务逻辑
 */
class TransferManager
{
    private $config;
    private $logger;

    public function __construct($config = [])
    {
        $this->config = $config;
        $this->logger = new Logger($config['debug_mode'] ?? false);
    }

    /**
     * 创建转移请求
     */
    public function createTransferRequest($serviceId, $fromClientId, $toEmail, $message = '')
    {
        try {
            // 验证服务
            $service = $this->validateService($serviceId, $fromClientId);
            if (!$service) {
                throw new Exception('Service not found or access denied');
            }

            // 检查目标用户
            $toClient = $this->findClientByEmail($toEmail);
            if (!$toClient) {
                throw new Exception('Target client not found');
            }

            if ($toClient->id == $fromClientId) {
                throw new Exception('Cannot transfer to yourself');
            }

            // 检查转移限制
            $this->checkTransferLimits($serviceId, $fromClientId);

            // 检查是否已有待处理的请求
            $existingRequest = Capsule::table('mod_pve_transfer_requests')
                ->where('service_id', $serviceId)
                ->where('status', 'pending')
                ->first();

            if ($existingRequest) {
                throw new Exception('There is already a pending transfer request for this service');
            }

            // 生成验证令牌
            $verificationToken = bin2hex(random_bytes(32));

            // 计算过期时间
            $expiresAt = date('Y-m-d H:i:s', strtotime('+' . ($this->config['auto_expire_hours'] ?? 72) . ' hours'));

            // 创建转移请求
            $requestId = Capsule::table('mod_pve_transfer_requests')->insertGetId([
                'service_id' => $serviceId,
                'from_client_id' => $fromClientId,
                'to_client_id' => $toClient->id,
                'to_email' => $toEmail,
                'status' => ($this->config['require_admin_approval'] ?? false) ? 'pending' : 'pending',
                'message' => $message,
                'verification_token' => $verificationToken,
                'expires_at' => $expiresAt,
                'created_at' => date('Y-m-d H:i:s'),
                'updated_at' => date('Y-m-d H:i:s'),
            ]);

            // 发送通知邮件
            if ($this->config['email_notifications'] ?? true) {
                $this->sendTransferNotification($requestId, 'created');
            }

            $this->logger->log("Transfer request created: ID {$requestId}, Service {$serviceId}");

            return [
                'success' => true,
                'request_id' => $requestId,
                'message' => 'Transfer request created successfully'
            ];

        } catch (Exception $e) {
            $this->logger->log("Failed to create transfer request: " . $e->getMessage());
            return [
                'success' => false,
                'message' => $e->getMessage()
            ];
        }
    }

    /**
     * 接受转移请求
     */
    public function acceptTransferRequest($requestId, $clientId, $verificationToken = null)
    {
        try {
            $request = Capsule::table('mod_pve_transfer_requests')
                ->where('id', $requestId)
                ->first();

            if (!$request) {
                throw new Exception('Transfer request not found');
            }

            // 验证权限
            if ($request->to_client_id != $clientId) {
                throw new Exception('Access denied');
            }

            // 验证令牌（如果提供）
            if ($verificationToken && $request->verification_token !== $verificationToken) {
                throw new Exception('Invalid verification token');
            }

            // 检查状态
            if ($request->status !== 'pending') {
                throw new Exception('Transfer request is not pending');
            }

            // 检查是否过期
            if (strtotime($request->expires_at) < time()) {
                $this->expireTransferRequest($requestId);
                throw new Exception('Transfer request has expired');
            }

            // 执行转移
            $transferResult = $this->executeTransfer($request);

            if ($transferResult['success']) {
                // 更新请求状态
                Capsule::table('mod_pve_transfer_requests')
                    ->where('id', $requestId)
                    ->update([
                        'status' => 'completed',
                        'updated_at' => date('Y-m-d H:i:s'),
                    ]);

                // 记录历史
                $this->recordTransferHistory($request, 'completed');

                // 发送完成通知
                if ($this->config['email_notifications'] ?? true) {
                    $this->sendTransferNotification($requestId, 'completed');
                }

                $this->logger->log("Transfer completed: Request {$requestId}");

                return [
                    'success' => true,
                    'message' => 'Transfer completed successfully'
                ];
            } else {
                throw new Exception($transferResult['message']);
            }

        } catch (Exception $e) {
            $this->logger->log("Failed to accept transfer: " . $e->getMessage());
            return [
                'success' => false,
                'message' => $e->getMessage()
            ];
        }
    }

    /**
     * 拒绝转移请求
     */
    public function rejectTransferRequest($requestId, $clientId, $reason = '')
    {
        try {
            $request = Capsule::table('mod_pve_transfer_requests')
                ->where('id', $requestId)
                ->first();

            if (!$request) {
                throw new Exception('Transfer request not found');
            }

            // 验证权限（接收方或管理员可以拒绝）
            if ($request->to_client_id != $clientId && !$this->isAdmin($clientId)) {
                throw new Exception('Access denied');
            }

            // 更新状态
            Capsule::table('mod_pve_transfer_requests')
                ->where('id', $requestId)
                ->update([
                    'status' => 'rejected',
                    'message' => $reason,
                    'updated_at' => date('Y-m-d H:i:s'),
                ]);

            // 记录历史
            $this->recordTransferHistory($request, 'rejected');

            // 发送通知
            if ($this->config['email_notifications'] ?? true) {
                $this->sendTransferNotification($requestId, 'rejected');
            }

            $this->logger->log("Transfer rejected: Request {$requestId}");

            return [
                'success' => true,
                'message' => 'Transfer request rejected'
            ];

        } catch (Exception $e) {
            $this->logger->log("Failed to reject transfer: " . $e->getMessage());
            return [
                'success' => false,
                'message' => $e->getMessage()
            ];
        }
    }

    /**
     * 执行实际的转移操作
     */
    private function executeTransfer($request)
    {
        try {
            // 开始数据库事务
            Capsule::beginTransaction();

            // 1. 更新WHMCS核心服务表
            Capsule::table('tblhosting')
                ->where('id', $request->service_id)
                ->update(['userid' => $request->to_client_id]);

            // 2. 同步PVE插件相关表
            $pveIntegration = new PVEIntegration();
            $pveResult = $pveIntegration->syncTransfer($request->service_id, $request->to_client_id);

            if (!$pveResult['success']) {
                throw new Exception('PVE integration failed: ' . $pveResult['message']);
            }

            // 3. 更新其他相关数据
            $this->updateRelatedData($request->service_id, $request->to_client_id);

            // 提交事务
            Capsule::commit();

            return [
                'success' => true,
                'message' => 'Transfer executed successfully'
            ];

        } catch (Exception $e) {
            // 回滚事务
            Capsule::rollback();
            
            return [
                'success' => false,
                'message' => $e->getMessage()
            ];
        }
    }

    /**
     * 验证服务
     */
    private function validateService($serviceId, $clientId)
    {
        return Capsule::table('tblhosting as h')
            ->join('tblproducts as p', 'h.packageid', '=', 'p.id')
            ->where('h.id', $serviceId)
            ->where('h.userid', $clientId)
            ->where('p.servertype', 'proxmoxVPS')
            ->where('h.domainstatus', 'Active')
            ->first(['h.*', 'p.name as product_name']);
    }

    /**
     * 通过邮箱查找客户
     */
    private function findClientByEmail($email)
    {
        return Capsule::table('tblclients')
            ->where('email', $email)
            ->first();
    }

    /**
     * 检查转移限制
     */
    private function checkTransferLimits($serviceId, $clientId)
    {
        $limitHours = $this->config['transfer_limit_hours'] ?? 24;
        
        if ($limitHours > 0) {
            $recentTransfer = Capsule::table('mod_pve_transfer_history')
                ->where('service_id', $serviceId)
                ->where('completed_at', '>', date('Y-m-d H:i:s', strtotime("-{$limitHours} hours")))
                ->first();

            if ($recentTransfer) {
                throw new Exception("Transfer limit: Must wait {$limitHours} hours between transfers");
            }
        }
    }

    /**
     * 记录转移历史
     */
    private function recordTransferHistory($request, $status)
    {
        $fromClient = Capsule::table('tblclients')->where('id', $request->from_client_id)->first();
        
        Capsule::table('mod_pve_transfer_history')->insert([
            'request_id' => $request->id,
            'service_id' => $request->service_id,
            'from_client_id' => $request->from_client_id,
            'to_client_id' => $request->to_client_id,
            'from_email' => $fromClient->email ?? '',
            'to_email' => $request->to_email,
            'message' => $request->message,
            'status' => $status,
            'completed_at' => date('Y-m-d H:i:s'),
            'metadata' => json_encode([
                'user_agent' => $_SERVER['HTTP_USER_AGENT'] ?? '',
                'ip_address' => $_SERVER['REMOTE_ADDR'] ?? '',
            ])
        ]);
    }

    /**
     * 发送转移通知邮件
     */
    private function sendTransferNotification($requestId, $type)
    {
        $notificationService = new NotificationService($this->config);
        return $notificationService->sendTransferEmail($requestId, $type);
    }

    /**
     * 过期转移请求
     */
    private function expireTransferRequest($requestId)
    {
        Capsule::table('mod_pve_transfer_requests')
            ->where('id', $requestId)
            ->update([
                'status' => 'expired',
                'updated_at' => date('Y-m-d H:i:s'),
            ]);
    }

    /**
     * 检查是否为管理员
     */
    private function isAdmin($clientId)
    {
        // 这里可以添加管理员检查逻辑
        return false;
    }

    /**
     * 更新相关数据
     */
    private function updateRelatedData($serviceId, $newClientId)
    {
        // 更新自定义字段等其他相关数据
        // 这里可以根据需要添加更多逻辑

        // 更新服务的自定义字段值
        try {
            Capsule::table('tblcustomfieldsvalues')
                ->where('relid', $serviceId)
                ->where('fieldid', 'IN', function($query) {
                    $query->select('id')
                          ->from('tblcustomfields')
                          ->where('type', 'product');
                })
                ->update(['updated_at' => date('Y-m-d H:i:s')]);
        } catch (Exception $e) {
            $this->logger->log("Warning: Failed to update custom fields: " . $e->getMessage());
        }
    }

    /**
     * 获取转移请求列表
     */
    public function getTransferRequests($clientId, $type = 'all', $limit = 50)
    {
        try {
            $query = Capsule::table('mod_pve_transfer_requests as r')
                ->join('tblhosting as h', 'r.service_id', '=', 'h.id')
                ->join('tblproducts as p', 'h.packageid', '=', 'p.id')
                ->leftJoin('tblclients as from_client', 'r.from_client_id', '=', 'from_client.id')
                ->leftJoin('tblclients as to_client', 'r.to_client_id', '=', 'to_client.id');

            switch ($type) {
                case 'sent':
                    $query->where('r.from_client_id', $clientId);
                    break;
                case 'received':
                    $query->where('r.to_client_id', $clientId);
                    break;
                default:
                    $query->where(function($q) use ($clientId) {
                        $q->where('r.from_client_id', $clientId)
                          ->orWhere('r.to_client_id', $clientId);
                    });
            }

            return $query->select([
                    'r.*',
                    'h.domain as service_domain',
                    'p.name as product_name',
                    'from_client.firstname as from_firstname',
                    'from_client.lastname as from_lastname',
                    'from_client.email as from_email',
                    'to_client.firstname as to_firstname',
                    'to_client.lastname as to_lastname'
                ])
                ->orderBy('r.created_at', 'desc')
                ->limit($limit)
                ->get();

        } catch (Exception $e) {
            $this->logger->log("Failed to get transfer requests: " . $e->getMessage());
            return collect();
        }
    }

    /**
     * 获取客户的可转移服务列表
     */
    public function getTransferableServices($clientId)
    {
        try {
            return Capsule::table('tblhosting as h')
                ->join('tblproducts as p', 'h.packageid', '=', 'p.id')
                ->leftJoin('mod_pve_transfer_requests as r', function($join) {
                    $join->on('h.id', '=', 'r.service_id')
                         ->where('r.status', '=', 'pending');
                })
                ->where('h.userid', $clientId)
                ->where('p.servertype', 'proxmoxVPS')
                ->where('h.domainstatus', 'Active')
                ->whereNull('r.id') // 排除已有待处理转移的服务
                ->select([
                    'h.id',
                    'h.domain',
                    'h.regdate',
                    'h.nextduedate',
                    'p.name as product_name'
                ])
                ->orderBy('h.domain')
                ->get();

        } catch (Exception $e) {
            $this->logger->log("Failed to get transferable services: " . $e->getMessage());
            return collect();
        }
    }

    /**
     * 清理过期的转移请求
     */
    public function cleanupExpiredRequests()
    {
        try {
            $expiredRequests = Capsule::table('mod_pve_transfer_requests')
                ->where('status', 'pending')
                ->where('expires_at', '<', date('Y-m-d H:i:s'))
                ->get();

            foreach ($expiredRequests as $request) {
                $this->expireTransferRequest($request->id);
                $this->recordTransferHistory($request, 'expired');
            }

            $this->logger->log("Cleaned up " . count($expiredRequests) . " expired transfer requests");

            return count($expiredRequests);

        } catch (Exception $e) {
            $this->logger->log("Failed to cleanup expired requests: " . $e->getMessage());
            return 0;
        }
    }
}
