<?php

namespace PVETransfer;

use WHMCS\Database\Capsule;
use WHMCS\Authentication\CurrentUser;

/**
 * 客户区域控制器
 */
class ClientController
{
    private $config;
    private $transferManager;
    private $currentUser;

    public function __construct($config)
    {
        $this->config = $config;
        $this->transferManager = new TransferManager($config);
        $this->currentUser = new CurrentUser();
    }

    /**
     * 处理客户区域请求
     */
    public function handleRequest()
    {
        // 检查模块是否启用
        if (!($this->config['enabled'] ?? true)) {
            return $this->renderError('Transfer module is currently disabled');
        }

        // 检查用户是否登录
        if (!$this->currentUser->isLoggedIn()) {
            return $this->renderError('Please login to access this feature');
        }

        $clientId = $this->currentUser->client()->id;

        // 检查用户组权限
        if (!$this->checkUserGroupPermission($clientId)) {
            return $this->renderError('You do not have permission to use the transfer feature');
        }

        $action = $_REQUEST['action'] ?? 'dashboard';

        switch ($action) {
            case 'create':
                return $this->handleCreateTransfer($clientId);
            case 'accept':
                return $this->handleAcceptTransfer($clientId);
            case 'reject':
                return $this->handleRejectTransfer($clientId);
            case 'cancel':
                return $this->handleCancelTransfer($clientId);
            case 'history':
                return $this->showTransferHistory($clientId);
            case 'verify':
                return $this->handleVerifyTransfer($clientId);
            default:
                return $this->showDashboard($clientId);
        }
    }

    /**
     * 显示主面板
     */
    private function showDashboard($clientId)
    {
        $data = [
            'transferable_services' => $this->transferManager->getTransferableServices($clientId),
            'pending_sent' => $this->transferManager->getTransferRequests($clientId, 'sent')->where('status', 'pending'),
            'pending_received' => $this->transferManager->getTransferRequests($clientId, 'received')->where('status', 'pending'),
            'recent_history' => $this->transferManager->getTransferRequests($clientId, 'all', 10),
        ];

        return [
            'pagetitle' => 'Product Transfer',
            'breadcrumb' => [
                'index.php?m=pve_transfer' => 'Product Transfer'
            ],
            'templatefile' => 'dashboard',
            'vars' => $data
        ];
    }

    /**
     * 处理创建转移请求
     */
    private function handleCreateTransfer($clientId)
    {
        if ($_SERVER['REQUEST_METHOD'] === 'POST') {
            $serviceId = (int)$_POST['service_id'];
            $toEmail = trim($_POST['to_email']);
            $message = trim($_POST['message'] ?? '');

            // 验证输入
            if (!$serviceId || !$toEmail) {
                return $this->renderError('Please fill in all required fields');
            }

            if (!filter_var($toEmail, FILTER_VALIDATE_EMAIL)) {
                return $this->renderError('Please enter a valid email address');
            }

            // 创建转移请求
            $result = $this->transferManager->createTransferRequest($serviceId, $clientId, $toEmail, $message);

            if ($result['success']) {
                return $this->renderSuccess('Transfer request created successfully. The recipient will receive an email notification.');
            } else {
                return $this->renderError($result['message']);
            }
        }

        // 显示创建表单
        $serviceId = (int)($_GET['service_id'] ?? 0);
        $service = null;

        if ($serviceId) {
            $transferableServices = $this->transferManager->getTransferableServices($clientId);
            $service = $transferableServices->where('id', $serviceId)->first();
        }

        return [
            'pagetitle' => 'Create Transfer Request',
            'breadcrumb' => [
                'index.php?m=pve_transfer' => 'Product Transfer',
                'index.php?m=pve_transfer&action=create' => 'Create Transfer'
            ],
            'templatefile' => 'create',
            'vars' => [
                'service' => $service,
                'transferable_services' => $this->transferManager->getTransferableServices($clientId)
            ]
        ];
    }

    /**
     * 处理接受转移请求
     */
    private function handleAcceptTransfer($clientId)
    {
        $requestId = (int)($_REQUEST['request_id'] ?? 0);
        $token = $_REQUEST['token'] ?? '';

        if (!$requestId) {
            return $this->renderError('Invalid transfer request');
        }

        if ($_SERVER['REQUEST_METHOD'] === 'POST') {
            $result = $this->transferManager->acceptTransferRequest($requestId, $clientId, $token);

            if ($result['success']) {
                return $this->renderSuccess('Transfer completed successfully! The service has been transferred to your account.');
            } else {
                return $this->renderError($result['message']);
            }
        }

        // 显示确认页面
        $request = $this->getTransferRequestDetails($requestId, $clientId);
        
        if (!$request) {
            return $this->renderError('Transfer request not found or access denied');
        }

        return [
            'pagetitle' => 'Accept Transfer',
            'breadcrumb' => [
                'index.php?m=pve_transfer' => 'Product Transfer',
                'index.php?m=pve_transfer&action=accept' => 'Accept Transfer'
            ],
            'templatefile' => 'accept',
            'vars' => [
                'request' => $request,
                'token' => $token
            ]
        ];
    }

    /**
     * 处理拒绝转移请求
     */
    private function handleRejectTransfer($clientId)
    {
        $requestId = (int)($_REQUEST['request_id'] ?? 0);

        if (!$requestId) {
            return $this->renderError('Invalid transfer request');
        }

        if ($_SERVER['REQUEST_METHOD'] === 'POST') {
            $reason = trim($_POST['reason'] ?? '');
            $result = $this->transferManager->rejectTransferRequest($requestId, $clientId, $reason);

            if ($result['success']) {
                return $this->renderSuccess('Transfer request rejected successfully.');
            } else {
                return $this->renderError($result['message']);
            }
        }

        // 显示拒绝表单
        $request = $this->getTransferRequestDetails($requestId, $clientId);
        
        if (!$request) {
            return $this->renderError('Transfer request not found or access denied');
        }

        return [
            'pagetitle' => 'Reject Transfer',
            'breadcrumb' => [
                'index.php?m=pve_transfer' => 'Product Transfer',
                'index.php?m=pve_transfer&action=reject' => 'Reject Transfer'
            ],
            'templatefile' => 'reject',
            'vars' => [
                'request' => $request
            ]
        ];
    }

    /**
     * 显示转移历史
     */
    private function showTransferHistory($clientId)
    {
        $type = $_GET['type'] ?? 'all';
        $page = max(1, (int)($_GET['page'] ?? 1));
        $limit = 20;

        $requests = $this->transferManager->getTransferRequests($clientId, $type, $limit);

        return [
            'pagetitle' => 'Transfer History',
            'breadcrumb' => [
                'index.php?m=pve_transfer' => 'Product Transfer',
                'index.php?m=pve_transfer&action=history' => 'Transfer History'
            ],
            'templatefile' => 'history',
            'vars' => [
                'requests' => $requests,
                'current_type' => $type,
                'current_page' => $page
            ]
        ];
    }

    /**
     * 处理邮件验证链接
     */
    private function handleVerifyTransfer($clientId)
    {
        $token = $_GET['token'] ?? '';
        $requestId = (int)($_GET['request_id'] ?? 0);

        if (!$token || !$requestId) {
            return $this->renderError('Invalid verification link');
        }

        // 验证令牌并显示接受页面
        return $this->handleAcceptTransfer($clientId);
    }

    /**
     * 获取转移请求详情
     */
    private function getTransferRequestDetails($requestId, $clientId)
    {
        try {
            return Capsule::table('mod_pve_transfer_requests as r')
                ->join('tblhosting as h', 'r.service_id', '=', 'h.id')
                ->join('tblproducts as p', 'h.packageid', '=', 'p.id')
                ->join('tblclients as from_client', 'r.from_client_id', '=', 'from_client.id')
                ->where('r.id', $requestId)
                ->where(function($query) use ($clientId) {
                    $query->where('r.from_client_id', $clientId)
                          ->orWhere('r.to_client_id', $clientId);
                })
                ->select([
                    'r.*',
                    'h.domain as service_domain',
                    'p.name as product_name',
                    'from_client.firstname as from_firstname',
                    'from_client.lastname as from_lastname',
                    'from_client.email as from_email'
                ])
                ->first();
        } catch (Exception $e) {
            return null;
        }
    }

    /**
     * 检查用户组权限
     */
    private function checkUserGroupPermission($clientId)
    {
        $allowedGroups = $this->config['allowed_groups'] ?? '';
        
        if (empty($allowedGroups)) {
            return true; // 如果没有限制，允许所有用户
        }

        $allowedGroupIds = array_map('trim', explode(',', $allowedGroups));
        
        $client = Capsule::table('tblclients')
            ->where('id', $clientId)
            ->first(['groupid']);

        return in_array($client->groupid ?? 0, $allowedGroupIds);
    }

    /**
     * 渲染错误页面
     */
    private function renderError($message)
    {
        return [
            'pagetitle' => 'Error',
            'breadcrumb' => [
                'index.php?m=pve_transfer' => 'Product Transfer'
            ],
            'templatefile' => 'error',
            'vars' => [
                'error_message' => $message
            ]
        ];
    }

    /**
     * 渲染成功页面
     */
    private function renderSuccess($message)
    {
        return [
            'pagetitle' => 'Success',
            'breadcrumb' => [
                'index.php?m=pve_transfer' => 'Product Transfer'
            ],
            'templatefile' => 'success',
            'vars' => [
                'success_message' => $message
            ]
        ];
    }
}
