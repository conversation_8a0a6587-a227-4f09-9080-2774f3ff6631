<?php

namespace PVETransfer;

/**
 * 日志记录类
 */
class Logger
{
    private $debugMode;
    private $logPrefix = 'PVETransfer';

    public function __construct($debugMode = false)
    {
        $this->debugMode = $debugMode;
    }

    /**
     * 记录日志
     */
    public function log($message, $level = 'info')
    {
        $timestamp = date('Y-m-d H:i:s');
        $logMessage = "[{$timestamp}] {$this->logPrefix} [{$level}]: {$message}";

        // 写入WHMCS活动日志
        if (function_exists('logActivity')) {
            logActivity($logMessage);
        }

        // 如果启用调试模式，也写入错误日志
        if ($this->debugMode) {
            error_log($logMessage);
        }
    }

    /**
     * 记录错误
     */
    public function error($message)
    {
        $this->log($message, 'error');
    }

    /**
     * 记录警告
     */
    public function warning($message)
    {
        $this->log($message, 'warning');
    }

    /**
     * 记录调试信息
     */
    public function debug($message)
    {
        if ($this->debugMode) {
            $this->log($message, 'debug');
        }
    }
}
