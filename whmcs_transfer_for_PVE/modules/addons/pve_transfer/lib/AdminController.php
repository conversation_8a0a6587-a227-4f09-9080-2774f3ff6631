<?php

namespace PVETransfer;

use WHMCS\Database\Capsule;

/**
 * 管理员区域控制器
 */
class AdminController
{
    private $config;
    private $transferManager;

    public function __construct($config)
    {
        $this->config = $config;
        $this->transferManager = new TransferManager($config);
    }

    /**
     * 处理管理员请求
     */
    public function handleRequest()
    {
        $action = $_REQUEST['action'] ?? 'dashboard';

        switch ($action) {
            case 'approve':
                return $this->handleApproveTransfer();
            case 'reject':
                return $this->handleRejectTransfer();
            case 'history':
                return $this->showTransferHistory();
            case 'statistics':
                return $this->showStatistics();
            case 'cleanup':
                return $this->handleCleanup();
            case 'settings':
                return $this->showSettings();
            default:
                return $this->showDashboard();
        }
    }

    /**
     * 显示管理员主面板
     */
    private function showDashboard()
    {
        $stats = $this->getTransferStatistics();
        $pendingRequests = $this->getPendingRequests();
        $recentTransfers = $this->getRecentTransfers();

        $output = '
        <div class="row">
            <div class="col-md-12">
                <h2>PVE Transfer Management</h2>
            </div>
        </div>

        <div class="row">
            <div class="col-md-3">
                <div class="panel panel-default">
                    <div class="panel-heading">
                        <h3 class="panel-title">Pending Requests</h3>
                    </div>
                    <div class="panel-body text-center">
                        <h2 class="text-warning">' . $stats['pending'] . '</h2>
                    </div>
                </div>
            </div>
            <div class="col-md-3">
                <div class="panel panel-default">
                    <div class="panel-heading">
                        <h3 class="panel-title">Completed Today</h3>
                    </div>
                    <div class="panel-body text-center">
                        <h2 class="text-success">' . $stats['completed_today'] . '</h2>
                    </div>
                </div>
            </div>
            <div class="col-md-3">
                <div class="panel panel-default">
                    <div class="panel-heading">
                        <h3 class="panel-title">Total Transfers</h3>
                    </div>
                    <div class="panel-body text-center">
                        <h2 class="text-info">' . $stats['total'] . '</h2>
                    </div>
                </div>
            </div>
            <div class="col-md-3">
                <div class="panel panel-default">
                    <div class="panel-heading">
                        <h3 class="panel-title">Success Rate</h3>
                    </div>
                    <div class="panel-body text-center">
                        <h2 class="text-primary">' . $stats['success_rate'] . '%</h2>
                    </div>
                </div>
            </div>
        </div>';

        if (!empty($pendingRequests)) {
            $output .= '
            <div class="row">
                <div class="col-md-12">
                    <div class="panel panel-default">
                        <div class="panel-heading">
                            <h3 class="panel-title">Pending Transfer Requests</h3>
                        </div>
                        <div class="panel-body">
                            <div class="table-responsive">
                                <table class="table table-striped">
                                    <thead>
                                        <tr>
                                            <th>ID</th>
                                            <th>Service</th>
                                            <th>From</th>
                                            <th>To</th>
                                            <th>Created</th>
                                            <th>Expires</th>
                                            <th>Actions</th>
                                        </tr>
                                    </thead>
                                    <tbody>';

            foreach ($pendingRequests as $request) {
                $output .= '
                                        <tr>
                                            <td>' . $request->id . '</td>
                                            <td>' . htmlspecialchars($request->service_domain) . '</td>
                                            <td>' . htmlspecialchars($request->from_email) . '</td>
                                            <td>' . htmlspecialchars($request->to_email) . '</td>
                                            <td>' . date('Y-m-d H:i', strtotime($request->created_at)) . '</td>
                                            <td>' . date('Y-m-d H:i', strtotime($request->expires_at)) . '</td>
                                            <td>
                                                <a href="?module=pve_transfer&action=approve&id=' . $request->id . '" 
                                                   class="btn btn-success btn-sm">Approve</a>
                                                <a href="?module=pve_transfer&action=reject&id=' . $request->id . '" 
                                                   class="btn btn-danger btn-sm">Reject</a>
                                            </td>
                                        </tr>';
            }

            $output .= '
                                    </tbody>
                                </table>
                            </div>
                        </div>
                    </div>
                </div>
            </div>';
        }

        $output .= '
        <div class="row">
            <div class="col-md-12">
                <div class="panel panel-default">
                    <div class="panel-heading">
                        <h3 class="panel-title">Quick Actions</h3>
                    </div>
                    <div class="panel-body">
                        <a href="?module=pve_transfer&action=history" class="btn btn-primary">View Transfer History</a>
                        <a href="?module=pve_transfer&action=statistics" class="btn btn-info">View Statistics</a>
                        <a href="?module=pve_transfer&action=cleanup" class="btn btn-warning">Cleanup Expired</a>
                        <a href="?module=pve_transfer&action=settings" class="btn btn-default">Settings</a>
                    </div>
                </div>
            </div>
        </div>';

        return $output;
    }

    /**
     * 获取转移统计数据
     */
    private function getTransferStatistics()
    {
        $pending = Capsule::table('mod_pve_transfer_requests')
            ->where('status', 'pending')
            ->count();

        $completedToday = Capsule::table('mod_pve_transfer_history')
            ->where('status', 'completed')
            ->whereDate('completed_at', date('Y-m-d'))
            ->count();

        $total = Capsule::table('mod_pve_transfer_history')->count();

        $completed = Capsule::table('mod_pve_transfer_history')
            ->where('status', 'completed')
            ->count();

        $successRate = $total > 0 ? round(($completed / $total) * 100, 1) : 0;

        return [
            'pending' => $pending,
            'completed_today' => $completedToday,
            'total' => $total,
            'success_rate' => $successRate
        ];
    }

    /**
     * 获取待处理请求
     */
    private function getPendingRequests()
    {
        return Capsule::table('mod_pve_transfer_requests as r')
            ->join('tblhosting as h', 'r.service_id', '=', 'h.id')
            ->join('tblclients as from_client', 'r.from_client_id', '=', 'from_client.id')
            ->where('r.status', 'pending')
            ->select([
                'r.*',
                'h.domain as service_domain',
                'from_client.email as from_email'
            ])
            ->orderBy('r.created_at', 'desc')
            ->limit(10)
            ->get();
    }

    /**
     * 获取最近转移记录
     */
    private function getRecentTransfers()
    {
        return Capsule::table('mod_pve_transfer_history')
            ->orderBy('completed_at', 'desc')
            ->limit(10)
            ->get();
    }

    /**
     * 处理批准转移
     */
    private function handleApproveTransfer()
    {
        $requestId = (int)($_GET['id'] ?? 0);
        
        if (!$requestId) {
            return '<div class="alert alert-danger">Invalid request ID</div>';
        }

        // 这里可以添加管理员批准逻辑
        // 目前直接返回信息，实际实现需要调用TransferManager的相应方法

        return '<div class="alert alert-success">Transfer request approved successfully</div>' . 
               $this->showDashboard();
    }

    /**
     * 处理拒绝转移
     */
    private function handleRejectTransfer()
    {
        $requestId = (int)($_GET['id'] ?? 0);
        
        if (!$requestId) {
            return '<div class="alert alert-danger">Invalid request ID</div>';
        }

        if ($_SERVER['REQUEST_METHOD'] === 'POST') {
            $reason = $_POST['reason'] ?? '';
            // 调用TransferManager拒绝转移
            // $result = $this->transferManager->rejectTransferRequest($requestId, 0, $reason);
            
            return '<div class="alert alert-success">Transfer request rejected successfully</div>' . 
                   $this->showDashboard();
        }

        // 显示拒绝表单
        return '
        <div class="panel panel-default">
            <div class="panel-heading">
                <h3 class="panel-title">Reject Transfer Request</h3>
            </div>
            <div class="panel-body">
                <form method="post">
                    <div class="form-group">
                        <label for="reason">Reason for rejection:</label>
                        <textarea name="reason" id="reason" class="form-control" rows="3"></textarea>
                    </div>
                    <button type="submit" class="btn btn-danger">Reject Transfer</button>
                    <a href="?module=pve_transfer" class="btn btn-default">Cancel</a>
                </form>
            </div>
        </div>';
    }

    /**
     * 显示转移历史
     */
    private function showTransferHistory()
    {
        $page = max(1, (int)($_GET['page'] ?? 1));
        $limit = 50;
        $offset = ($page - 1) * $limit;

        $history = Capsule::table('mod_pve_transfer_history as h')
            ->leftJoin('tblhosting as hosting', 'h.service_id', '=', 'hosting.id')
            ->select([
                'h.*',
                'hosting.domain as service_domain'
            ])
            ->orderBy('h.completed_at', 'desc')
            ->limit($limit)
            ->offset($offset)
            ->get();

        $total = Capsule::table('mod_pve_transfer_history')->count();
        $totalPages = ceil($total / $limit);

        $output = '
        <div class="panel panel-default">
            <div class="panel-heading">
                <h3 class="panel-title">Transfer History</h3>
            </div>
            <div class="panel-body">
                <div class="table-responsive">
                    <table class="table table-striped">
                        <thead>
                            <tr>
                                <th>ID</th>
                                <th>Service</th>
                                <th>From</th>
                                <th>To</th>
                                <th>Status</th>
                                <th>Completed</th>
                            </tr>
                        </thead>
                        <tbody>';

        foreach ($history as $record) {
            $statusClass = $record->status === 'completed' ? 'success' : 
                          ($record->status === 'rejected' ? 'danger' : 'warning');
            
            $output .= '
                            <tr>
                                <td>' . $record->id . '</td>
                                <td>' . htmlspecialchars($record->service_domain ?? 'N/A') . '</td>
                                <td>' . htmlspecialchars($record->from_email) . '</td>
                                <td>' . htmlspecialchars($record->to_email) . '</td>
                                <td><span class="label label-' . $statusClass . '">' . 
                                    ucfirst($record->status) . '</span></td>
                                <td>' . date('Y-m-d H:i', strtotime($record->completed_at)) . '</td>
                            </tr>';
        }

        $output .= '
                        </tbody>
                    </table>
                </div>';

        // 分页
        if ($totalPages > 1) {
            $output .= '<nav><ul class="pagination">';
            
            for ($i = 1; $i <= $totalPages; $i++) {
                $active = $i === $page ? ' class="active"' : '';
                $output .= '<li' . $active . '><a href="?module=pve_transfer&action=history&page=' . $i . '">' . $i . '</a></li>';
            }
            
            $output .= '</ul></nav>';
        }

        $output .= '
            </div>
        </div>
        <a href="?module=pve_transfer" class="btn btn-default">Back to Dashboard</a>';

        return $output;
    }

    /**
     * 显示统计信息
     */
    private function showStatistics()
    {
        // 获取详细统计数据
        $monthlyStats = $this->getMonthlyStatistics();
        $statusStats = $this->getStatusStatistics();

        $output = '
        <div class="row">
            <div class="col-md-6">
                <div class="panel panel-default">
                    <div class="panel-heading">
                        <h3 class="panel-title">Monthly Transfer Statistics</h3>
                    </div>
                    <div class="panel-body">
                        <canvas id="monthlyChart" width="400" height="200"></canvas>
                    </div>
                </div>
            </div>
            <div class="col-md-6">
                <div class="panel panel-default">
                    <div class="panel-heading">
                        <h3 class="panel-title">Transfer Status Distribution</h3>
                    </div>
                    <div class="panel-body">
                        <canvas id="statusChart" width="400" height="200"></canvas>
                    </div>
                </div>
            </div>
        </div>
        <a href="?module=pve_transfer" class="btn btn-default">Back to Dashboard</a>';

        return $output;
    }

    /**
     * 处理清理操作
     */
    private function handleCleanup()
    {
        $cleaned = $this->transferManager->cleanupExpiredRequests();
        
        return '<div class="alert alert-success">Cleaned up ' . $cleaned . ' expired transfer requests</div>' . 
               $this->showDashboard();
    }

    /**
     * 显示设置页面
     */
    private function showSettings()
    {
        return '
        <div class="panel panel-default">
            <div class="panel-heading">
                <h3 class="panel-title">PVE Transfer Settings</h3>
            </div>
            <div class="panel-body">
                <p>Module settings can be configured in the addon modules configuration page.</p>
                <a href="configaddonmods.php" class="btn btn-primary">Go to Addon Modules</a>
            </div>
        </div>
        <a href="?module=pve_transfer" class="btn btn-default">Back to Dashboard</a>';
    }

    /**
     * 获取月度统计
     */
    private function getMonthlyStatistics()
    {
        return Capsule::table('mod_pve_transfer_history')
            ->selectRaw('DATE_FORMAT(completed_at, "%Y-%m") as month, COUNT(*) as count')
            ->where('completed_at', '>=', date('Y-m-d', strtotime('-12 months')))
            ->groupBy('month')
            ->orderBy('month')
            ->get();
    }

    /**
     * 获取状态统计
     */
    private function getStatusStatistics()
    {
        return Capsule::table('mod_pve_transfer_history')
            ->selectRaw('status, COUNT(*) as count')
            ->groupBy('status')
            ->get();
    }
}
