<?php
/**
 * WHMCS Transfer for PVE - Main Module File
 * 
 * 专为WHMCS和Proxmox VE插件设计的产品转移解决方案
 * 
 * @package    PVE_Transfer
 * <AUTHOR> Name
 * @copyright  2025 Your Company
 * @license    MIT License
 * @version    1.0.0
 */

if (!defined("WHMCS")) {
    die("This file cannot be accessed directly");
}

use WHMCS\Database\Capsule;

/**
 * 模块配置信息
 */
function pve_transfer_config()
{
    return [
        'name' => 'PVE Transfer',
        'description' => 'Professional product transfer solution for WHMCS and Proxmox VE',
        'version' => '1.0.0',
        'author' => 'Your Name',
        'language' => 'english',
        'fields' => [
            'enabled' => [
                'FriendlyName' => 'Enable Module',
                'Type' => 'yesno',
                'Description' => 'Enable the PVE Transfer module',
                'Default' => 'yes',
            ],
            'allowed_groups' => [
                'FriendlyName' => 'Allowed Client Groups',
                'Type' => 'text',
                'Size' => '50',
                'Description' => 'Comma-separated list of client group IDs that can use transfer (empty = all)',
                'Default' => '',
            ],
            'require_admin_approval' => [
                'FriendlyName' => 'Require Admin Approval',
                'Type' => 'yesno',
                'Description' => 'Require administrator approval for transfers',
                'Default' => 'no',
            ],
            'email_notifications' => [
                'FriendlyName' => 'Email Notifications',
                'Type' => 'yesno',
                'Description' => 'Send email notifications for transfer events',
                'Default' => 'yes',
            ],
            'transfer_limit_hours' => [
                'FriendlyName' => 'Transfer Limit (Hours)',
                'Type' => 'text',
                'Size' => '10',
                'Description' => 'Minimum hours between transfers for same service (0 = no limit)',
                'Default' => '24',
            ],
            'auto_expire_hours' => [
                'FriendlyName' => 'Auto Expire (Hours)',
                'Type' => 'text',
                'Size' => '10',
                'Description' => 'Hours after which pending transfers auto-expire',
                'Default' => '72',
            ],
            'debug_mode' => [
                'FriendlyName' => 'Debug Mode',
                'Type' => 'yesno',
                'Description' => 'Enable debug logging',
                'Default' => 'no',
            ],
        ]
    ];
}

/**
 * 模块激活时执行
 */
function pve_transfer_activate()
{
    try {
        // 创建转移请求表
        if (!Capsule::schema()->hasTable('mod_pve_transfer_requests')) {
            Capsule::schema()->create('mod_pve_transfer_requests', function ($table) {
                $table->increments('id');
                $table->integer('service_id')->unsigned();
                $table->integer('from_client_id')->unsigned();
                $table->integer('to_client_id')->unsigned();
                $table->string('to_email', 255);
                $table->enum('status', ['pending', 'approved', 'rejected', 'expired', 'completed'])->default('pending');
                $table->text('message')->nullable();
                $table->string('verification_token', 64);
                $table->timestamp('expires_at');
                $table->timestamp('created_at')->useCurrent();
                $table->timestamp('updated_at')->useCurrent();
                $table->integer('created_by')->unsigned()->nullable();
                $table->integer('approved_by')->unsigned()->nullable();
                $table->timestamp('approved_at')->nullable();
                
                $table->index(['service_id']);
                $table->index(['from_client_id']);
                $table->index(['to_client_id']);
                $table->index(['status']);
                $table->index(['verification_token']);
            });
        }

        // 创建转移历史表
        if (!Capsule::schema()->hasTable('mod_pve_transfer_history')) {
            Capsule::schema()->create('mod_pve_transfer_history', function ($table) {
                $table->increments('id');
                $table->integer('request_id')->unsigned();
                $table->integer('service_id')->unsigned();
                $table->integer('from_client_id')->unsigned();
                $table->integer('to_client_id')->unsigned();
                $table->string('from_email', 255);
                $table->string('to_email', 255);
                $table->text('message')->nullable();
                $table->enum('status', ['completed', 'rejected', 'expired']);
                $table->timestamp('completed_at');
                $table->integer('completed_by')->unsigned()->nullable();
                $table->json('metadata')->nullable(); // 存储额外信息
                
                $table->index(['service_id']);
                $table->index(['from_client_id']);
                $table->index(['to_client_id']);
                $table->index(['status']);
                $table->index(['completed_at']);
            });
        }

        // 创建配置表
        if (!Capsule::schema()->hasTable('mod_pve_transfer_config')) {
            Capsule::schema()->create('mod_pve_transfer_config', function ($table) {
                $table->increments('id');
                $table->string('setting_key', 100)->unique();
                $table->text('setting_value');
                $table->timestamp('created_at')->useCurrent();
                $table->timestamp('updated_at')->useCurrent();
            });
        }

        // 插入默认配置
        $defaultConfigs = [
            ['setting_key' => 'module_version', 'setting_value' => '1.0.0'],
            ['setting_key' => 'last_cleanup', 'setting_value' => date('Y-m-d H:i:s')],
        ];

        foreach ($defaultConfigs as $config) {
            Capsule::table('mod_pve_transfer_config')
                ->updateOrInsert(
                    ['setting_key' => $config['setting_key']],
                    $config
                );
        }

        return [
            'status' => 'success',
            'description' => 'PVE Transfer module activated successfully. Database tables created.'
        ];

    } catch (Exception $e) {
        return [
            'status' => 'error',
            'description' => 'Failed to activate module: ' . $e->getMessage()
        ];
    }
}

/**
 * 模块停用时执行
 */
function pve_transfer_deactivate()
{
    try {
        // 注意：我们不删除数据表，只是停用功能
        // 这样可以保留历史数据
        
        return [
            'status' => 'success',
            'description' => 'PVE Transfer module deactivated successfully. Data preserved.'
        ];

    } catch (Exception $e) {
        return [
            'status' => 'error',
            'description' => 'Failed to deactivate module: ' . $e->getMessage()
        ];
    }
}

/**
 * 模块升级时执行
 */
function pve_transfer_upgrade($vars)
{
    $currentVersion = $vars['version'];
    
    try {
        // 这里可以添加版本升级逻辑
        // 例如数据库结构更新等
        
        return [
            'status' => 'success',
            'description' => 'Module upgraded successfully to version ' . $currentVersion
        ];

    } catch (Exception $e) {
        return [
            'status' => 'error',
            'description' => 'Failed to upgrade module: ' . $e->getMessage()
        ];
    }
}

/**
 * 管理员区域输出
 */
function pve_transfer_output($vars)
{
    // 加载管理员界面控制器
    require_once __DIR__ . '/lib/AdminController.php';
    
    $controller = new PVETransfer\AdminController($vars);
    return $controller->handleRequest();
}

/**
 * 客户区域输出
 */
function pve_transfer_clientarea($vars)
{
    // 加载客户区域控制器
    require_once __DIR__ . '/lib/ClientController.php';
    
    $controller = new PVETransfer\ClientController($vars);
    return $controller->handleRequest();
}


