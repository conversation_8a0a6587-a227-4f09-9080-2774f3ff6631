<?php
/**
 * PVE Transfer Module Hooks
 * 
 * 这些钩子提供额外的功能和集成
 */

if (!defined("WHMCS")) {
    die("This file cannot be accessed directly");
}

use WHMCS\Database\Capsule;

/**
 * 每日清理过期的转移请求
 */
add_hook('DailyCronJob', 1, function($vars) {
    try {
        // 检查模块是否启用
        $moduleConfig = Capsule::table('tbladdonmodules')
            ->where('module', 'pve_transfer')
            ->where('setting', 'enabled')
            ->where('value', 'on')
            ->first();

        if (!$moduleConfig) {
            return; // 模块未启用
        }

        // 加载配置
        $config = [];
        $configRows = Capsule::table('tbladdonmodules')
            ->where('module', 'pve_transfer')
            ->get();

        foreach ($configRows as $row) {
            $config[$row->setting] = $row->value;
        }

        // 执行清理
        require_once __DIR__ . '/lib/TransferManager.php';
        $transferManager = new PVETransfer\TransferManager($config);
        $cleaned = $transferManager->cleanupExpiredRequests();

        if ($cleaned > 0) {
            logActivity("PVE Transfer: Cleaned up {$cleaned} expired transfer requests");
        }

    } catch (Exception $e) {
        logActivity("PVE Transfer Cleanup Error: " . $e->getMessage());
    }
});

/**
 * 在客户区域菜单中添加转移链接
 */
add_hook('ClientAreaPrimaryNavbar', 1, function($primaryNavbar) {
    try {
        // 检查模块是否启用
        $moduleConfig = Capsule::table('tbladdonmodules')
            ->where('module', 'pve_transfer')
            ->where('setting', 'enabled')
            ->where('value', 'on')
            ->first();

        if (!$moduleConfig) {
            return;
        }

        // 检查用户是否有Proxmox服务
        $currentUser = new WHMCS\Authentication\CurrentUser();
        if (!$currentUser->isAuthenticatedClient()) {
            return;
        }

        $clientId = $currentUser->client()->id;
        
        $hasProxmoxServices = Capsule::table('tblhosting as h')
            ->join('tblproducts as p', 'h.packageid', '=', 'p.id')
            ->where('h.userid', $clientId)
            ->where('p.servertype', 'proxmoxVPS')
            ->where('h.domainstatus', 'Active')
            ->exists();

        if ($hasProxmoxServices) {
            // 添加到服务菜单下
            $servicesMenu = $primaryNavbar->getChild('Services');
            if ($servicesMenu) {
                $servicesMenu->addChild('Product Transfer', [
                    'label' => 'Product Transfer',
                    'uri' => 'index.php?m=pve_transfer',
                    'order' => 100,
                ]);
            }
        }

    } catch (Exception $e) {
        logActivity("PVE Transfer Menu Error: " . $e->getMessage());
    }
});

/**
 * 在服务详情页面添加转移按钮
 */
add_hook('ClientAreaPageProductDetails', 1, function($vars) {
    try {
        // 检查是否为Proxmox服务
        if ($vars['producttype'] !== 'hostingaccount') {
            return;
        }

        $service = Capsule::table('tblhosting as h')
            ->join('tblproducts as p', 'h.packageid', '=', 'p.id')
            ->where('h.id', $vars['serviceid'])
            ->where('p.servertype', 'proxmoxVPS')
            ->first();

        if (!$service) {
            return;
        }

        // 检查是否已有待处理的转移
        $pendingTransfer = Capsule::table('mod_pve_transfer_requests')
            ->where('service_id', $vars['serviceid'])
            ->where('status', 'pending')
            ->first();

        if ($pendingTransfer) {
            return [
                'transfer_pending' => true,
                'transfer_expires' => $pendingTransfer->expires_at
            ];
        }

        return [
            'can_transfer' => true,
            'transfer_url' => 'index.php?m=pve_transfer&action=create&service_id=' . $vars['serviceid']
        ];

    } catch (Exception $e) {
        logActivity("PVE Transfer Service Page Error: " . $e->getMessage());
        return [];
    }
});

/**
 * 在管理员区域添加转移统计
 */
add_hook('AdminHomeWidgets', 1, function($vars) {
    try {
        // 检查模块是否启用
        $moduleConfig = Capsule::table('tbladdonmodules')
            ->where('module', 'pve_transfer')
            ->where('setting', 'enabled')
            ->where('value', 'on')
            ->first();

        if (!$moduleConfig) {
            return;
        }

        // 获取待处理的转移数量
        $pendingCount = Capsule::table('mod_pve_transfer_requests')
            ->where('status', 'pending')
            ->count();

        if ($pendingCount > 0) {
            return [
                'title' => 'PVE Transfers',
                'content' => "
                    <div class='widget-content-padded'>
                        <div class='list-group'>
                            <div class='list-group-item'>
                                <div class='list-group-item-heading'>
                                    <i class='fas fa-exchange-alt'></i> Pending Transfer Requests
                                </div>
                                <div class='list-group-item-text'>
                                    <strong>{$pendingCount}</strong> requests awaiting action
                                    <a href='addonmodules.php?module=pve_transfer' class='btn btn-sm btn-primary pull-right'>
                                        Manage
                                    </a>
                                </div>
                            </div>
                        </div>
                    </div>
                ",
                'order' => 100
            ];
        }

    } catch (Exception $e) {
        logActivity("PVE Transfer Widget Error: " . $e->getMessage());
    }

    return [];
});

/**
 * 记录转移相关的活动
 */
add_hook('AfterModuleCreate', 1, function($vars) {
    if ($vars['producttype'] === 'hostingaccount') {
        $service = Capsule::table('tblhosting as h')
            ->join('tblproducts as p', 'h.packageid', '=', 'p.id')
            ->where('h.id', $vars['serviceid'])
            ->where('p.servertype', 'proxmoxVPS')
            ->first();

        if ($service) {
            logActivity("PVE Service Created: Service #{$vars['serviceid']} for client #{$vars['userid']}");
        }
    }
});

/**
 * 在服务暂停/取消暂停时检查转移状态
 */
add_hook('PreModuleSuspend', 1, function($vars) {
    if ($vars['producttype'] === 'hostingaccount') {
        // 检查是否有待处理的转移
        $pendingTransfer = Capsule::table('mod_pve_transfer_requests')
            ->where('service_id', $vars['serviceid'])
            ->where('status', 'pending')
            ->first();

        if ($pendingTransfer) {
            // 可以选择阻止暂停或自动取消转移
            logActivity("PVE Transfer: Service #{$vars['serviceid']} suspended with pending transfer #{$pendingTransfer->id}");
        }
    }
});

/**
 * 服务终止时清理转移数据
 */
add_hook('AfterModuleTerminate', 1, function($vars) {
    if ($vars['producttype'] === 'hostingaccount') {
        try {
            // 取消所有待处理的转移请求
            $cancelledCount = Capsule::table('mod_pve_transfer_requests')
                ->where('service_id', $vars['serviceid'])
                ->where('status', 'pending')
                ->update([
                    'status' => 'cancelled',
                    'updated_at' => date('Y-m-d H:i:s')
                ]);

            if ($cancelledCount > 0) {
                logActivity("PVE Transfer: Cancelled {$cancelledCount} pending transfers for terminated service #{$vars['serviceid']}");
            }

        } catch (Exception $e) {
            logActivity("PVE Transfer Cleanup Error: " . $e->getMessage());
        }
    }
});

/**
 * 客户删除时处理转移数据
 */
add_hook('ClientDelete', 1, function($vars) {
    try {
        $clientId = $vars['userid'];

        // 取消该客户的所有待处理转移
        $cancelledCount = Capsule::table('mod_pve_transfer_requests')
            ->where(function($query) use ($clientId) {
                $query->where('from_client_id', $clientId)
                      ->orWhere('to_client_id', $clientId);
            })
            ->where('status', 'pending')
            ->update([
                'status' => 'cancelled',
                'updated_at' => date('Y-m-d H:i:s')
            ]);

        if ($cancelledCount > 0) {
            logActivity("PVE Transfer: Cancelled {$cancelledCount} pending transfers for deleted client #{$clientId}");
        }

    } catch (Exception $e) {
        logActivity("PVE Transfer Client Delete Error: " . $e->getMessage());
    }
});

/**
 * 添加管理员通知
 */
add_hook('AdminAreaFooterOutput', 1, function($vars) {
    try {
        // 只在相关页面显示
        if (!in_array($vars['filename'], ['addonmodules.php', 'clientssummary.php', 'clientsservices.php'])) {
            return '';
        }

        // 检查是否有需要注意的转移
        $urgentTransfers = Capsule::table('mod_pve_transfer_requests')
            ->where('status', 'pending')
            ->where('expires_at', '<', date('Y-m-d H:i:s', strtotime('+24 hours')))
            ->count();

        if ($urgentTransfers > 0) {
            return "
            <script>
            $(document).ready(function() {
                if ($('.alert-info:contains(\"PVE Transfer\")').length === 0) {
                    $('body').prepend('<div class=\"alert alert-info alert-dismissible\"><button type=\"button\" class=\"close\" data-dismiss=\"alert\">&times;</button><strong>PVE Transfer:</strong> {$urgentTransfers} transfer request(s) expiring within 24 hours. <a href=\"addonmodules.php?module=pve_transfer\">Review now</a></div>');
                }
            });
            </script>";
        }

    } catch (Exception $e) {
        return '';
    }

    return '';
});
