{*
 * PVE Transfer - Create Transfer Template
 *}

<div class="row">
    <div class="col-md-8 col-md-offset-2">
        <div class="panel panel-default">
            <div class="panel-heading">
                <h3 class="panel-title">
                    <i class="fas fa-share"></i> {$LANG.pvetransfer_create_title|default:"Create Transfer Request"}
                </h3>
            </div>
            <div class="panel-body">
                <form method="post" id="transferForm">
                    <div class="form-group">
                        <label for="service_id">{$LANG.pvetransfer_select_service|default:"Select Service"} <span class="text-danger">*</span></label>
                        <select name="service_id" id="service_id" class="form-control" required>
                            <option value="">{$LANG.pvetransfer_choose_service|default:"Choose a service to transfer..."}</option>
                            {foreach $transferable_services as $svc}
                            <option value="{$svc->id}" {if $service && $service->id == $svc->id}selected{/if}>
                                {$svc->domain} - {$svc->product_name}
                            </option>
                            {/foreach}
                        </select>
                        <small class="help-block">{$LANG.pvetransfer_service_help|default:"Only active Proxmox VPS services can be transferred."}</small>
                    </div>

                    <div class="form-group">
                        <label for="to_email">{$LANG.pvetransfer_recipient_email|default:"Recipient Email Address"} <span class="text-danger">*</span></label>
                        <input type="email" name="to_email" id="to_email" class="form-control" required 
                               placeholder="{$LANG.pvetransfer_email_placeholder|default:"Enter the email address of the person you want to transfer to"}">
                        <small class="help-block">{$LANG.pvetransfer_email_help|default:"The recipient must have an account with us. They will receive an email notification."}</small>
                    </div>

                    <div class="form-group">
                        <label for="message">{$LANG.pvetransfer_message|default:"Message (Optional)"}</label>
                        <textarea name="message" id="message" class="form-control" rows="3" 
                                  placeholder="{$LANG.pvetransfer_message_placeholder|default:"Add a personal message to the recipient..."}"></textarea>
                        <small class="help-block">{$LANG.pvetransfer_message_help|default:"This message will be included in the transfer notification email."}</small>
                    </div>

                    <div class="alert alert-info">
                        <h4><i class="fas fa-info-circle"></i> {$LANG.pvetransfer_important_info|default:"Important Information"}</h4>
                        <ul class="mb-0">
                            <li>{$LANG.pvetransfer_info1|default:"The recipient will receive an email with a verification link"}</li>
                            <li>{$LANG.pvetransfer_info2|default:"The transfer request will expire in 72 hours if not accepted"}</li>
                            <li>{$LANG.pvetransfer_info3|default:"Once accepted, the service will be immediately transferred to the recipient's account"}</li>
                            <li>{$LANG.pvetransfer_info4|default:"You will lose access to the service once the transfer is completed"}</li>
                        </ul>
                    </div>

                    <div class="form-group">
                        <div class="checkbox">
                            <label>
                                <input type="checkbox" id="confirm_transfer" required>
                                {$LANG.pvetransfer_confirm|default:"I understand that this action will transfer ownership of the selected service and I will lose access to it once completed."}
                            </label>
                        </div>
                    </div>

                    <div class="form-group">
                        <button type="submit" class="btn btn-primary" id="submitBtn" disabled>
                            <i class="fas fa-paper-plane"></i> {$LANG.pvetransfer_send_request|default:"Send Transfer Request"}
                        </button>
                        <a href="index.php?m=pve_transfer" class="btn btn-default">
                            <i class="fas fa-arrow-left"></i> {$LANG.pvetransfer_back|default:"Back"}
                        </a>
                    </div>
                </form>
            </div>
        </div>
    </div>
</div>

{if $service}
<div class="row">
    <div class="col-md-8 col-md-offset-2">
        <div class="panel panel-info">
            <div class="panel-heading">
                <h3 class="panel-title">
                    <i class="fas fa-server"></i> {$LANG.pvetransfer_service_details|default:"Service Details"}
                </h3>
            </div>
            <div class="panel-body">
                <div class="row">
                    <div class="col-md-6">
                        <strong>{$LANG.pvetransfer_domain|default:"Domain"}:</strong> {$service->domain}
                    </div>
                    <div class="col-md-6">
                        <strong>{$LANG.pvetransfer_product|default:"Product"}:</strong> {$service->product_name}
                    </div>
                </div>
                <div class="row" style="margin-top: 10px;">
                    <div class="col-md-6">
                        <strong>{$LANG.pvetransfer_created|default:"Created"}:</strong> {$service->regdate|date_format:"%Y-%m-%d"}
                    </div>
                    <div class="col-md-6">
                        <strong>{$LANG.pvetransfer_next_due|default:"Next Due"}:</strong> {$service->nextduedate|date_format:"%Y-%m-%d"}
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
{/if}

<script>
$(document).ready(function() {
    // Enable/disable submit button based on confirmation checkbox
    $('#confirm_transfer').change(function() {
        $('#submitBtn').prop('disabled', !this.checked);
    });

    // Form validation
    $('#transferForm').submit(function(e) {
        var serviceId = $('#service_id').val();
        var toEmail = $('#to_email').val();
        var confirmed = $('#confirm_transfer').is(':checked');

        if (!serviceId) {
            alert('{$LANG.pvetransfer_error_no_service|default:"Please select a service to transfer."}');
            e.preventDefault();
            return false;
        }

        if (!toEmail) {
            alert('{$LANG.pvetransfer_error_no_email|default:"Please enter the recipient\'s email address."}');
            e.preventDefault();
            return false;
        }

        if (!confirmed) {
            alert('{$LANG.pvetransfer_error_not_confirmed|default:"Please confirm that you understand the transfer process."}');
            e.preventDefault();
            return false;
        }

        // Show loading state
        $('#submitBtn').prop('disabled', true).html('<i class="fas fa-spinner fa-spin"></i> {$LANG.pvetransfer_processing|default:"Processing..."}');
        
        return true;
    });

    // Email validation
    $('#to_email').blur(function() {
        var email = $(this).val();
        if (email) {
            var emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
            if (!emailRegex.test(email)) {
                $(this).closest('.form-group').addClass('has-error');
                if ($(this).next('.help-block').find('.error-text').length === 0) {
                    $(this).next('.help-block').append('<br><span class="error-text text-danger">{$LANG.pvetransfer_invalid_email|default:"Please enter a valid email address."}</span>');
                }
            } else {
                $(this).closest('.form-group').removeClass('has-error');
                $(this).next('.help-block').find('.error-text').remove();
            }
        }
    });

    // Auto-select service if provided in URL
    {if $service}
    $('#service_id').val('{$service->id}').trigger('change');
    {/if}
});
</script>

<style>
.panel-heading .fas {
    margin-right: 5px;
}
.btn .fas {
    margin-right: 5px;
}
.alert ul {
    margin-bottom: 0;
}
.has-error .form-control {
    border-color: #a94442;
}
.error-text {
    font-weight: bold;
}
</style>
