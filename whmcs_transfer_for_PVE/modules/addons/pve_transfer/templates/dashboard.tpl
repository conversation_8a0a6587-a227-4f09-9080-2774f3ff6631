{*
 * PVE Transfer - Dashboard Template
 *}

<div class="row">
    <div class="col-md-12">
        <h2>{$LANG.pvetransfer_dashboard_title|default:"Product Transfer"}</h2>
        <p class="lead">{$LANG.pvetransfer_dashboard_subtitle|default:"Manage your product transfers easily and securely"}</p>
    </div>
</div>

{if $transferable_services->count() > 0}
<div class="row">
    <div class="col-md-6">
        <div class="panel panel-default">
            <div class="panel-heading">
                <h3 class="panel-title">
                    <i class="fas fa-server"></i> {$LANG.pvetransfer_your_services|default:"Your Transferable Services"}
                </h3>
            </div>
            <div class="panel-body">
                <div class="list-group">
                    {foreach $transferable_services as $service}
                    <div class="list-group-item">
                        <div class="row">
                            <div class="col-md-8">
                                <h5 class="list-group-item-heading">{$service->domain}</h5>
                                <p class="list-group-item-text text-muted">{$service->product_name}</p>
                            </div>
                            <div class="col-md-4 text-right">
                                <a href="index.php?m=pve_transfer&action=create&service_id={$service->id}" 
                                   class="btn btn-primary btn-sm">
                                    <i class="fas fa-share"></i> {$LANG.pvetransfer_transfer|default:"Transfer"}
                                </a>
                            </div>
                        </div>
                    </div>
                    {/foreach}
                </div>
            </div>
        </div>
    </div>
    
    <div class="col-md-6">
        <div class="panel panel-info">
            <div class="panel-heading">
                <h3 class="panel-title">
                    <i class="fas fa-info-circle"></i> {$LANG.pvetransfer_how_it_works|default:"How It Works"}
                </h3>
            </div>
            <div class="panel-body">
                <ol>
                    <li>{$LANG.pvetransfer_step1|default:"Select the service you want to transfer"}</li>
                    <li>{$LANG.pvetransfer_step2|default:"Enter the recipient's email address"}</li>
                    <li>{$LANG.pvetransfer_step3|default:"The recipient receives an email notification"}</li>
                    <li>{$LANG.pvetransfer_step4|default:"They accept the transfer and it's completed automatically"}</li>
                </ol>
                <div class="alert alert-info">
                    <i class="fas fa-shield-alt"></i> 
                    {$LANG.pvetransfer_security_note|default:"All transfers are secure and require email verification from both parties."}
                </div>
            </div>
        </div>
    </div>
</div>
{else}
<div class="row">
    <div class="col-md-12">
        <div class="alert alert-info">
            <i class="fas fa-info-circle"></i> 
            {$LANG.pvetransfer_no_services|default:"You don't have any transferable Proxmox VPS services at the moment."}
        </div>
    </div>
</div>
{/if}

{if $pending_sent->count() > 0 || $pending_received->count() > 0}
<div class="row">
    {if $pending_sent->count() > 0}
    <div class="col-md-6">
        <div class="panel panel-warning">
            <div class="panel-heading">
                <h3 class="panel-title">
                    <i class="fas fa-clock"></i> {$LANG.pvetransfer_pending_sent|default:"Pending Transfers (Sent)"}
                </h3>
            </div>
            <div class="panel-body">
                <div class="list-group">
                    {foreach $pending_sent as $request}
                    <div class="list-group-item">
                        <div class="row">
                            <div class="col-md-8">
                                <h5 class="list-group-item-heading">{$request->service_domain}</h5>
                                <p class="list-group-item-text">
                                    {$LANG.pvetransfer_to|default:"To"}: {$request->to_email}<br>
                                    <small class="text-muted">
                                        {$LANG.pvetransfer_expires|default:"Expires"}: {$request->expires_at|date_format:"%Y-%m-%d %H:%M"}
                                    </small>
                                </p>
                            </div>
                            <div class="col-md-4 text-right">
                                <span class="label label-warning">{$LANG.pvetransfer_pending|default:"Pending"}</span>
                            </div>
                        </div>
                    </div>
                    {/foreach}
                </div>
            </div>
        </div>
    </div>
    {/if}
    
    {if $pending_received->count() > 0}
    <div class="col-md-6">
        <div class="panel panel-success">
            <div class="panel-heading">
                <h3 class="panel-title">
                    <i class="fas fa-inbox"></i> {$LANG.pvetransfer_pending_received|default:"Pending Transfers (Received)"}
                </h3>
            </div>
            <div class="panel-body">
                <div class="list-group">
                    {foreach $pending_received as $request}
                    <div class="list-group-item">
                        <div class="row">
                            <div class="col-md-8">
                                <h5 class="list-group-item-heading">{$request->service_domain}</h5>
                                <p class="list-group-item-text">
                                    {$LANG.pvetransfer_from|default:"From"}: {$request->from_firstname} {$request->from_lastname}<br>
                                    <small class="text-muted">
                                        {$LANG.pvetransfer_expires|default:"Expires"}: {$request->expires_at|date_format:"%Y-%m-%d %H:%M"}
                                    </small>
                                </p>
                            </div>
                            <div class="col-md-4 text-right">
                                <a href="index.php?m=pve_transfer&action=accept&request_id={$request->id}" 
                                   class="btn btn-success btn-sm">
                                    <i class="fas fa-check"></i> {$LANG.pvetransfer_accept|default:"Accept"}
                                </a>
                                <a href="index.php?m=pve_transfer&action=reject&request_id={$request->id}" 
                                   class="btn btn-danger btn-sm">
                                    <i class="fas fa-times"></i> {$LANG.pvetransfer_reject|default:"Reject"}
                                </a>
                            </div>
                        </div>
                    </div>
                    {/foreach}
                </div>
            </div>
        </div>
    </div>
    {/if}
</div>
{/if}

{if $recent_history->count() > 0}
<div class="row">
    <div class="col-md-12">
        <div class="panel panel-default">
            <div class="panel-heading">
                <h3 class="panel-title">
                    <i class="fas fa-history"></i> {$LANG.pvetransfer_recent_history|default:"Recent Transfer History"}
                </h3>
            </div>
            <div class="panel-body">
                <div class="table-responsive">
                    <table class="table table-striped">
                        <thead>
                            <tr>
                                <th>{$LANG.pvetransfer_service|default:"Service"}</th>
                                <th>{$LANG.pvetransfer_from|default:"From"}</th>
                                <th>{$LANG.pvetransfer_to|default:"To"}</th>
                                <th>{$LANG.pvetransfer_status|default:"Status"}</th>
                                <th>{$LANG.pvetransfer_date|default:"Date"}</th>
                            </tr>
                        </thead>
                        <tbody>
                            {foreach $recent_history as $record}
                            <tr>
                                <td>{$record->service_domain}</td>
                                <td>{$record->from_firstname} {$record->from_lastname}</td>
                                <td>{$record->to_firstname} {$record->to_lastname}</td>
                                <td>
                                    {if $record->status == 'pending'}
                                        <span class="label label-warning">{$LANG.pvetransfer_pending|default:"Pending"}</span>
                                    {elseif $record->status == 'completed'}
                                        <span class="label label-success">{$LANG.pvetransfer_completed|default:"Completed"}</span>
                                    {elseif $record->status == 'rejected'}
                                        <span class="label label-danger">{$LANG.pvetransfer_rejected|default:"Rejected"}</span>
                                    {elseif $record->status == 'expired'}
                                        <span class="label label-default">{$LANG.pvetransfer_expired|default:"Expired"}</span>
                                    {/if}
                                </td>
                                <td>{$record->created_at|date_format:"%Y-%m-%d %H:%M"}</td>
                            </tr>
                            {/foreach}
                        </tbody>
                    </table>
                </div>
                <div class="text-center">
                    <a href="index.php?m=pve_transfer&action=history" class="btn btn-default">
                        {$LANG.pvetransfer_view_all_history|default:"View All History"}
                    </a>
                </div>
            </div>
        </div>
    </div>
</div>
{/if}

<style>
.panel-heading .fas {
    margin-right: 5px;
}
.list-group-item-heading {
    margin-bottom: 5px;
}
.btn-sm .fas {
    margin-right: 3px;
}
</style>
