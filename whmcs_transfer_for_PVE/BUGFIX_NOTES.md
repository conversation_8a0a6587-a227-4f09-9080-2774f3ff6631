# PVE Transfer Module - Bug Fix Notes

## 🐛 修复的问题

### 1. 方法名错误 (Critical)

**问题描述：**
在hooks.php和ClientController.php中使用了错误的方法名 `isAuthenticatedClient()`，导致"Call to undefined method"错误。

**错误信息：**
```
Call to undefined method WHMCS\Authentication\CurrentUser::isAuthenticatedClient()
```

**修复方案：**
将 `isAuthenticatedClient()` 更改为正确的 `isLoggedIn()` 方法。

**修复文件：**
- `lib/ClientController.php` - 第35行
- `includes/hooks/pve_transfer.php` - 第72行

### 2. 钩子文件位置错误 (Important)

**问题描述：**
钩子文件放置在模块目录内并在主模块文件中自动加载，这不符合WHMCS的最佳实践。

**修复方案：**
- 将钩子文件移动到 `includes/hooks/pve_transfer.php`
- 移除主模块文件中的自动加载代码
- 添加正确的模块激活/停用函数

**修复文件：**
- 新建：`includes/hooks/pve_transfer.php`
- 修改：`modules/addons/pve_transfer/pve_transfer.php`
- 删除：`modules/addons/pve_transfer/hooks.php`

### 3. 安装脚本更新 (Enhancement)

**更新内容：**
- 修正源文件路径引用
- 添加钩子文件的安装逻辑
- 更新权限设置命令
- 改进错误检查和用户反馈

## 🔧 技术细节

### WHMCS CurrentUser 类方法

正确的方法名称：
- ✅ `isLoggedIn()` - 检查用户是否已登录
- ❌ `isAuthenticatedClient()` - 此方法不存在

### WHMCS 钩子系统

正确的钩子文件位置：
- ✅ `includes/hooks/filename.php` - 推荐位置
- ✅ `modules/addons/modulename/hooks/filename.php` - 替代位置
- ❌ 在主模块文件中直接加载 - 不推荐

### 模块生命周期函数

添加的标准函数：
- `pve_transfer_activate()` - 模块激活时调用
- `pve_transfer_deactivate()` - 模块停用时调用

## 🚀 升级指南

### 如果您已经安装了旧版本：

1. **备份现有安装：**
   ```bash
   cp -r modules/addons/pve_transfer modules/addons/pve_transfer.backup
   ```

2. **停用模块：**
   - 在WHMCS管理员界面停用PVE Transfer模块

3. **更新文件：**
   ```bash
   # 删除旧的钩子文件
   rm -f modules/addons/pve_transfer/hooks.php
   
   # 复制新文件
   cp whmcs_transfer_for_PVE/modules/addons/pve_transfer/pve_transfer.php modules/addons/pve_transfer/
   cp whmcs_transfer_for_PVE/includes/hooks/pve_transfer.php includes/hooks/
   
   # 设置权限
   chmod 644 modules/addons/pve_transfer/pve_transfer.php
   chmod 644 includes/hooks/pve_transfer.php
   ```

4. **重新激活模块：**
   - 在WHMCS管理员界面重新激活PVE Transfer模块

### 全新安装：

使用更新后的安装脚本：
```bash
chmod +x whmcs_transfer_for_PVE/install.sh
./whmcs_transfer_for_PVE/install.sh
```

## ✅ 验证修复

### 1. 检查错误日志
确认不再出现 `isAuthenticatedClient` 相关错误。

### 2. 测试客户区域访问
访问 `index.php?m=pve_transfer` 确认页面正常加载。

### 3. 检查钩子功能
- 客户区域菜单中应显示"Product Transfer"链接
- 管理员主页应显示转移统计（如有待处理请求）

### 4. 验证模块状态
在 Setup → Addon Modules 中确认模块状态为"Active"。

## 📋 测试清单

- [ ] 客户区域可以正常访问转移页面
- [ ] 用户登录状态检查正常工作
- [ ] 钩子功能正常（菜单、统计等）
- [ ] 模块激活/停用功能正常
- [ ] 错误日志中无相关错误信息

## 🔍 故障排除

### 如果仍然出现错误：

1. **清理缓存：**
   ```bash
   # 清理OPcache（如果启用）
   service apache2 reload
   # 或
   service nginx reload
   ```

2. **检查文件权限：**
   ```bash
   ls -la includes/hooks/pve_transfer.php
   ls -la modules/addons/pve_transfer/pve_transfer.php
   ```

3. **检查PHP错误日志：**
   ```bash
   tail -f /var/log/php_errors.log
   ```

4. **验证WHMCS版本兼容性：**
   确保使用WHMCS 8.x或更高版本。

## 📞 支持

如果在升级过程中遇到问题：

1. 检查WHMCS活动日志中的相关错误信息
2. 确认所有文件都已正确上传和设置权限
3. 验证WHMCS和PHP版本符合要求
4. 如问题持续，请提供详细的错误日志信息

---

**这些修复确保了模块的稳定性和与WHMCS的完美兼容性！**
