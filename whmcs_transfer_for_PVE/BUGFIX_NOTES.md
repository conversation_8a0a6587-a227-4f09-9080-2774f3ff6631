# PVE Transfer Module - Bug Fix Notes

## 🐛 修复的问题

### 1. 函数重复定义错误 (Critical)

**问题描述：**
在主模块文件中，`pve_transfer_activate()` 和 `pve_transfer_deactivate()` 函数被定义了两次，导致"Cannot redeclare function"错误。

**错误信息：**
```
Fatal error: Cannot redeclare pve_transfer_activate() (previously declared in /var/www/html/whmcs/modules/addons/pve_transfer/pve_transfer.php:84)
```

**修复方案：**
删除重复的函数定义，只保留包含完整数据库创建逻辑的版本。

**修复文件：**
- `modules/addons/pve_transfer/pve_transfer.php` - 删除第245-262行的重复函数

### 2. CurrentUser方法错误 (Critical)

**问题描述：**
在钩子文件和ClientController中使用了不存在的 `isLoggedIn()` 方法，导致"Call to undefined method"错误。

**错误信息：**
```
Call to undefined method WHMCS\Authentication\CurrentUser::isLoggedIn()
```

**修复方案：**
根据WHMCS官方文档，CurrentUser类没有 `isLoggedIn()` 方法。正确的做法是检查 `client()` 方法是否返回null。

**修复前：**
```php
if (!$currentUser->isLoggedIn()) {
    return;
}
$clientId = $currentUser->client()->id;
```

**修复后：**
```php
$client = $currentUser->client();
if (!$client) {
    return; // 用户未登录
}
$clientId = $client->id;
```

**修复文件：**
- `lib/ClientController.php` - 第34-40行
- `includes/hooks/pve_transfer.php` - 第71-78行

### 2. 钩子文件位置错误 (Important)

**问题描述：**
钩子文件放置在模块目录内并在主模块文件中自动加载，这不符合WHMCS的最佳实践。

**修复方案：**
- 将钩子文件移动到 `includes/hooks/pve_transfer.php`
- 移除主模块文件中的自动加载代码
- 添加正确的模块激活/停用函数

**修复文件：**
- 新建：`includes/hooks/pve_transfer.php`
- 修改：`modules/addons/pve_transfer/pve_transfer.php`
- 删除：`modules/addons/pve_transfer/hooks.php`

### 3. 安装脚本更新 (Enhancement)

**更新内容：**
- 修正源文件路径引用
- 添加钩子文件的安装逻辑
- 更新权限设置命令
- 改进错误检查和用户反馈

## 🔧 技术细节

### WHMCS CurrentUser 类方法

正确的方法名称：
- ✅ `client()` - 返回当前登录的客户对象，未登录时返回null
- ✅ `user()` - 返回当前登录的用户对象，未登录时返回null
- ✅ `admin()` - 返回当前登录的管理员对象，未登录时返回null
- ✅ `isAuthenticatedUser()` - 检查是否以用户身份认证
- ✅ `isAuthenticatedAdmin()` - 检查是否以管理员身份认证
- ❌ `isLoggedIn()` - 此方法不存在
- ❌ `isAuthenticatedClient()` - 此方法不存在

**正确的登录检查方式：**
```php
$currentUser = new WHMCS\Authentication\CurrentUser();
$client = $currentUser->client();
if ($client) {
    // 用户已登录
    $clientId = $client->id;
} else {
    // 用户未登录
}
```

### WHMCS 钩子系统

正确的钩子文件位置：
- ✅ `includes/hooks/filename.php` - 推荐位置
- ✅ `modules/addons/modulename/hooks/filename.php` - 替代位置
- ❌ 在主模块文件中直接加载 - 不推荐

### 模块生命周期函数

添加的标准函数：
- `pve_transfer_activate()` - 模块激活时调用
- `pve_transfer_deactivate()` - 模块停用时调用

## 🚀 升级指南

### 方法一：使用清理重装脚本（推荐）

```bash
# 运行自动清理和重装脚本
chmod +x whmcs_transfer_for_PVE/cleanup_and_reinstall.sh
./whmcs_transfer_for_PVE/cleanup_and_reinstall.sh
```

### 方法二：手动升级

1. **备份现有安装：**
   ```bash
   cp -r modules/addons/pve_transfer modules/addons/pve_transfer.backup
   ```

2. **停用模块：**
   - 在WHMCS管理员界面停用PVE Transfer模块

3. **清理旧文件：**
   ```bash
   # 删除整个模块目录
   rm -rf modules/addons/pve_transfer

   # 删除旧的钩子文件
   rm -f includes/hooks/pve_transfer.php
   ```

4. **重新安装：**
   ```bash
   # 运行安装脚本
   chmod +x whmcs_transfer_for_PVE/install.sh
   ./whmcs_transfer_for_PVE/install.sh
   ```

5. **清理缓存：**
   ```bash
   # 重启Web服务器清理OPcache
   sudo service apache2 reload
   # 或
   sudo service nginx reload
   ```

6. **重新激活模块：**
   - 在WHMCS管理员界面重新激活PVE Transfer模块

### 全新安装：

使用更新后的安装脚本：
```bash
chmod +x whmcs_transfer_for_PVE/install.sh
./whmcs_transfer_for_PVE/install.sh
```

## ✅ 验证修复

### 1. 检查错误日志
确认不再出现 `isAuthenticatedClient` 相关错误。

### 2. 测试客户区域访问
访问 `index.php?m=pve_transfer` 确认页面正常加载。

### 3. 检查钩子功能
- 客户区域菜单中应显示"Product Transfer"链接
- 管理员主页应显示转移统计（如有待处理请求）

### 4. 验证模块状态
在 Setup → Addon Modules 中确认模块状态为"Active"。

## 📋 测试清单

- [ ] 客户区域可以正常访问转移页面
- [ ] 用户登录状态检查正常工作
- [ ] 钩子功能正常（菜单、统计等）
- [ ] 模块激活/停用功能正常
- [ ] 错误日志中无相关错误信息

## 🔍 故障排除

### 如果仍然出现错误：

1. **函数重复定义错误：**
   ```bash
   # 使用清理重装脚本完全重新安装
   ./whmcs_transfer_for_PVE/cleanup_and_reinstall.sh
   ```

2. **清理缓存：**
   ```bash
   # 清理OPcache（如果启用）
   service apache2 reload
   # 或
   service nginx reload

   # 或者使用PHP命令清理
   php -r "if (function_exists('opcache_reset')) opcache_reset();"
   ```

3. **检查文件权限：**
   ```bash
   ls -la includes/hooks/pve_transfer.php
   ls -la modules/addons/pve_transfer/pve_transfer.php
   ```

4. **检查PHP语法：**
   ```bash
   php -l modules/addons/pve_transfer/pve_transfer.php
   php -l includes/hooks/pve_transfer.php
   ```

5. **检查PHP错误日志：**
   ```bash
   tail -f /var/log/php_errors.log
   ```

6. **验证WHMCS版本兼容性：**
   确保使用WHMCS 8.x或更高版本。

## 📞 支持

如果在升级过程中遇到问题：

1. 检查WHMCS活动日志中的相关错误信息
2. 确认所有文件都已正确上传和设置权限
3. 验证WHMCS和PHP版本符合要求
4. 如问题持续，请提供详细的错误日志信息

---

**这些修复确保了模块的稳定性和与WHMCS的完美兼容性！**
