#!/bin/bash

# PVE Transfer Module - Cleanup and Reinstall Script
# 用于清理旧文件并重新安装模块

echo "=========================================="
echo "PVE Transfer Module - Cleanup & Reinstall"
echo "=========================================="

# 检查是否在WHMCS目录中
if [ ! -f "configuration.php" ]; then
    echo "❌ 错误: 请在WHMCS根目录中运行此脚本"
    exit 1
fi

echo "✅ 检测到WHMCS安装目录"

# 停用模块（如果可能）
echo "🔄 准备清理旧文件..."

# 备份现有配置（如果存在）
if [ -d "modules/addons/pve_transfer" ]; then
    echo "📦 备份现有模块..."
    BACKUP_DIR="modules/addons/pve_transfer.backup.$(date +%Y%m%d_%H%M%S)"
    mv "modules/addons/pve_transfer" "$BACKUP_DIR"
    echo "  ✅ 备份到: $BACKUP_DIR"
fi

# 删除旧的钩子文件
if [ -f "includes/hooks/pve_transfer.php" ]; then
    echo "🗑️  删除旧钩子文件..."
    rm -f "includes/hooks/pve_transfer.php"
    echo "  ✅ 旧钩子文件已删除"
fi

# 清理可能的缓存
echo "🧹 清理缓存..."

# 清理OPcache（如果可用）
if command -v php >/dev/null 2>&1; then
    php -r "if (function_exists('opcache_reset')) { opcache_reset(); echo 'OPcache cleared\n'; }"
fi

# 清理可能的临时文件
find . -name "*.tmp" -type f -delete 2>/dev/null || true
find . -name "*~" -type f -delete 2>/dev/null || true

echo "✅ 清理完成"

# 重新安装
echo ""
echo "🚀 开始重新安装..."

# 检查源文件
SOURCE_DIR="whmcs_transfer_for_PVE"
if [ ! -d "$SOURCE_DIR" ]; then
    echo "❌ 错误: 找不到源文件目录 $SOURCE_DIR"
    echo "   请确保在包含 whmcs_transfer_for_PVE 目录的位置运行此脚本"
    exit 1
fi

# 创建目标目录
TARGET_DIR="modules/addons/pve_transfer"
mkdir -p "$TARGET_DIR"
mkdir -p "$TARGET_DIR/lib"
mkdir -p "$TARGET_DIR/templates"
mkdir -p "includes/hooks"

# 复制文件
echo "📁 复制模块文件..."
cp "$SOURCE_DIR/modules/addons/pve_transfer/pve_transfer.php" "$TARGET_DIR/"
cp -r "$SOURCE_DIR/modules/addons/pve_transfer/lib/"* "$TARGET_DIR/lib/"
cp -r "$SOURCE_DIR/modules/addons/pve_transfer/templates/"* "$TARGET_DIR/templates/"

echo "📎 复制钩子文件..."
cp "$SOURCE_DIR/includes/hooks/pve_transfer.php" "includes/hooks/"

# 设置权限
echo "🔐 设置文件权限..."
chmod 644 "$TARGET_DIR/pve_transfer.php"
chmod -R 644 "$TARGET_DIR/lib/"
chmod -R 644 "$TARGET_DIR/templates/"
chmod 644 "includes/hooks/pve_transfer.php"

echo "✅ 文件安装完成"

# 验证安装
echo ""
echo "🔍 验证安装..."

# 检查关键文件
REQUIRED_FILES=(
    "$TARGET_DIR/pve_transfer.php"
    "$TARGET_DIR/lib/TransferManager.php"
    "$TARGET_DIR/lib/ClientController.php"
    "$TARGET_DIR/lib/AdminController.php"
    "includes/hooks/pve_transfer.php"
)

ALL_GOOD=true
for file in "${REQUIRED_FILES[@]}"; do
    if [ -f "$file" ]; then
        echo "  ✅ $file"
    else
        echo "  ❌ $file (缺失)"
        ALL_GOOD=false
    fi
done

if [ "$ALL_GOOD" = true ]; then
    echo "✅ 所有文件安装正确"
else
    echo "❌ 部分文件缺失，请检查安装"
    exit 1
fi

# 运行完整验证
echo ""
echo "🔍 运行完整验证..."
if [ -f "$SOURCE_DIR/verify_fixes.php" ]; then
    cp "$SOURCE_DIR/verify_fixes.php" "verify_fixes.php"
    php verify_fixes.php
    VERIFY_RESULT=$?
    rm -f verify_fixes.php

    if [ $VERIFY_RESULT -ne 0 ]; then
        echo "❌ 验证失败，请检查错误信息"
        exit 1
    fi
else
    echo "⚠️  验证脚本不存在，进行基本语法检查..."

    # 基本PHP语法检查
    php -l "$TARGET_DIR/pve_transfer.php" >/dev/null 2>&1
    if [ $? -eq 0 ]; then
        echo "  ✅ 主模块文件语法正确"
    else
        echo "  ❌ 主模块文件语法错误"
        php -l "$TARGET_DIR/pve_transfer.php"
        exit 1
    fi

    php -l "includes/hooks/pve_transfer.php" >/dev/null 2>&1
    if [ $? -eq 0 ]; then
        echo "  ✅ 钩子文件语法正确"
    else
        echo "  ❌ 钩子文件语法错误"
        php -l "includes/hooks/pve_transfer.php"
        exit 1
    fi
fi

# 重启Web服务器（可选）
echo ""
echo "🔄 建议重启Web服务器以清理缓存..."
echo "   Apache: sudo service apache2 reload"
echo "   Nginx:  sudo service nginx reload"
echo "   或者等待几分钟让缓存自动过期"

echo ""
echo "🎉 重新安装完成！"
echo ""
echo "📋 下一步操作："
echo "1. 在WHMCS管理员界面访问 Setup → Addon Modules"
echo "2. 找到 'PVE Transfer' 模块"
echo "3. 如果显示为已激活，先点击 'Deactivate' 然后再点击 'Activate'"
echo "4. 如果显示为未激活，直接点击 'Activate'"
echo "5. 配置模块设置"
echo ""
echo "🔍 测试访问："
echo "   客户区域: https://your-domain.com/index.php?m=pve_transfer"
echo "   管理员界面: Setup → Addon Modules → PVE Transfer"
echo ""
echo "⚠️  如果仍然出现错误，请："
echo "1. 检查WHMCS错误日志"
echo "2. 确认PHP版本兼容性"
echo "3. 验证文件权限设置"
echo ""
echo "=========================================="

exit 0
