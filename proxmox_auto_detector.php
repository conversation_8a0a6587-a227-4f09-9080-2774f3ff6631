<?php
/**
 * Proxmox Auto Detector Hook
 * 自动检测和修复Proxmox数据不一致问题
 * 
 * 这个hook会在多个关键点检查数据一致性：
 * 1. 客户区域访问Proxmox服务时
 * 2. 管理员查看Proxmox服务时
 * 3. 模块操作执行前
 */

use WHMCS\Database\Capsule;

/**
 * Hook: 客户区域页面加载时检查
 */
add_hook('ClientAreaPage', 1, function ($vars) {
    // 检查是否在服务详情页面
    if (isset($vars['templatefile']) && $vars['templatefile'] === 'clientareaproductdetails') {
        if (isset($_GET['id'])) {
            $serviceId = (int)$_GET['id'];
            autoCheckAndFixService($serviceId, 'client_area');
        }
    }
});

/**
 * Hook: 模块操作前检查
 */
add_hook('PreModuleCreate', 1, function ($vars) {
    if (isset($vars['producttype']) && $vars['producttype'] === 'hostingaccount') {
        if (isset($vars['serviceid'])) {
            autoCheckAndFixService($vars['serviceid'], 'module_create');
        }
    }
});

add_hook('PreModuleSuspend', 1, function ($vars) {
    if (isset($vars['serviceid'])) {
        autoCheckAndFixService($vars['serviceid'], 'module_suspend');
    }
});

add_hook('PreModuleUnsuspend', 1, function ($vars) {
    if (isset($vars['serviceid'])) {
        autoCheckAndFixService($vars['serviceid'], 'module_unsuspend');
    }
});

add_hook('PreModuleTerminate', 1, function ($vars) {
    if (isset($vars['serviceid'])) {
        autoCheckAndFixService($vars['serviceid'], 'module_terminate');
    }
});

/**
 * Hook: 管理员区域特定操作
 */
add_hook('AdminAreaPage', 1, function ($vars) {
    // 在服务管理页面检查
    if (isset($vars['filename'])) {
        $checkPages = [
            'clientsservices.php',
            'clientssummary.php', 
            'clientsproducts.php',
            'modulesgarden.php' // Proxmox addon页面
        ];
        
        if (in_array($vars['filename'], $checkPages)) {
            // 检查URL参数中的服务ID
            if (isset($_GET['id'])) {
                autoCheckAndFixService((int)$_GET['id'], 'admin_area');
            }
            
            // 检查POST数据中的服务ID
            if (isset($_POST['id'])) {
                autoCheckAndFixService((int)$_POST['id'], 'admin_post');
            }
        }
    }
});

/**
 * 自动检查和修复服务
 */
function autoCheckAndFixService($serviceId, $context = 'unknown') {
    try {
        if ($serviceId <= 0) {
            return;
        }
        
        // 检查是否为Proxmox服务
        $service = Capsule::table('tblhosting as h')
            ->join('tblproducts as p', 'h.packageid', '=', 'p.id')
            ->where('h.id', $serviceId)
            ->where('p.servertype', 'proxmoxVPS')
            ->first(['h.id', 'h.userid', 'h.domain', 'h.domainstatus']);
            
        if (!$service) {
            return; // 不是Proxmox服务
        }
        
        // 检查Proxmox插件数据
        $proxmoxService = Capsule::table('mg_proxmoxaddon_services')
            ->where('service_id', $serviceId)
            ->first(['client_id']);
            
        $needsFix = false;
        $reason = '';
        
        if (!$proxmoxService) {
            $needsFix = true;
            $reason = 'missing_mapping';
        } elseif ($proxmoxService->client_id != $service->userid) {
            $needsFix = true;
            $reason = 'client_mismatch';
        }
        
        if ($needsFix) {
            // 记录检测到的问题
            logActivity("ProxmoxAutoDetector: Inconsistency detected for service #{$serviceId} " .
                      "({$service->domain}) in context '{$context}' - reason: {$reason}");
            
            // 执行自动修复
            $fixed = performAutoFix($serviceId, $service->userid, $service->domain);
            
            if ($fixed) {
                logActivity("ProxmoxAutoDetector: Auto-fixed service #{$serviceId} " .
                          "({$service->domain}) → client #{$service->userid}");
            }
        }
        
    } catch (Exception $e) {
        logActivity("ProxmoxAutoDetector Error: " . $e->getMessage());
    }
}

/**
 * 执行自动修复
 */
function performAutoFix($serviceId, $clientId, $domain) {
    try {
        // 更新或插入服务映射
        Capsule::table('mg_proxmoxaddon_services')
            ->updateOrInsert(
                ['service_id' => $serviceId],
                [
                    'client_id' => $clientId,
                    'updated_at' => date('Y-m-d H:i:s')
                ]
            );
        
        // 确保用户映射存在
        $userExists = Capsule::table('mg_proxmoxaddon_users')
            ->where('client_id', $clientId)
            ->exists();
            
        if (!$userExists) {
            Capsule::table('mg_proxmoxaddon_users')
                ->insert([
                    'client_id' => $clientId,
                    'realm' => 'pve',
                    'pve_user' => 'vm' . $clientId,
                    'created_at' => date('Y-m-d H:i:s'),
                    'updated_at' => date('Y-m-d H:i:s')
                ]);
        }
        
        // 更新相关表（如果存在）
        $relatedTables = [
            'mg_proxmoxaddon_vms',
            'mg_proxmoxaddon_ips', 
            'ProxmoxAddon_Backups'
        ];
        
        foreach ($relatedTables as $table) {
            try {
                Capsule::table($table)
                    ->where('service_id', $serviceId)
                    ->update(['client_id' => $clientId]);
            } catch (Exception $e) {
                // 表可能不存在，忽略错误
            }
        }
        
        // 清理缓存
        try {
            Capsule::table('ProxmoxAddon_ModuleCache')->truncate();
        } catch (Exception $e) {
            // 忽略缓存清理错误
        }
        
        return true;
        
    } catch (Exception $e) {
        logActivity("ProxmoxAutoDetector Fix Error: " . $e->getMessage());
        return false;
    }
}

/**
 * Hook: 定期批量检查（每日）
 */
add_hook('DailyCronJob', 1, function ($vars) {
    performDailyConsistencyCheck();
});

/**
 * 执行每日一致性检查
 */
function performDailyConsistencyCheck() {
    try {
        logActivity("ProxmoxAutoDetector: Starting daily consistency check...");
        
        // 查找所有不一致的Proxmox服务
        $inconsistentServices = Capsule::table('tblhosting as h')
            ->join('tblproducts as p', 'h.packageid', '=', 'p.id')
            ->leftJoin('mg_proxmoxaddon_services as ps', 'h.id', '=', 'ps.service_id')
            ->where('p.servertype', 'proxmoxVPS')
            ->where('h.domainstatus', 'Active') // 只检查活跃服务
            ->where(function($query) {
                $query->whereNull('ps.service_id')
                      ->orWhere('ps.client_id', '!=', Capsule::raw('h.userid'));
            })
            ->select([
                'h.id as service_id',
                'h.userid as correct_client_id',
                'h.domain',
                'ps.client_id as current_client_id'
            ])
            ->get();
        
        if ($inconsistentServices->isEmpty()) {
            logActivity("ProxmoxAutoDetector: Daily check completed - no inconsistencies found");
            return;
        }
        
        $fixedCount = 0;
        $errorCount = 0;
        
        foreach ($inconsistentServices as $service) {
            $fixed = performAutoFix(
                $service->service_id, 
                $service->correct_client_id, 
                $service->domain
            );
            
            if ($fixed) {
                $fixedCount++;
            } else {
                $errorCount++;
            }
        }
        
        logActivity("ProxmoxAutoDetector: Daily check completed - " .
                   "fixed: {$fixedCount}, errors: {$errorCount}, " .
                   "total checked: " . count($inconsistentServices));
        
    } catch (Exception $e) {
        logActivity("ProxmoxAutoDetector Daily Check Error: " . $e->getMessage());
    }
}

/**
 * Hook: 在Proxmox模块调用时进行最后检查
 */
add_hook('PreModuleCustomButton', 1, function ($vars) {
    if (isset($vars['serviceid'])) {
        autoCheckAndFixService($vars['serviceid'], 'custom_button');
    }
});

/**
 * 提供一个API端点用于手动触发检查
 */
add_hook('AdminAreaPage', 1, function ($vars) {
    if (isset($_GET['proxmox_check']) && isset($_GET['service_id'])) {
        $serviceId = (int)$_GET['service_id'];
        if ($serviceId > 0) {
            autoCheckAndFixService($serviceId, 'manual_trigger');
            
            // 返回JSON响应
            if (isset($_GET['json'])) {
                header('Content-Type: application/json');
                echo json_encode(['status' => 'checked', 'service_id' => $serviceId]);
                exit;
            }
        }
    }
});
