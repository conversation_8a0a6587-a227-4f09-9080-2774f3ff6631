<?php
/**
 * Proxmox Manual Fix Tool
 * 手动修复WHMCS ownership transfer后的Proxmox数据不同步问题
 * 
 * 使用方法：
 * 1. 上传到WHMCS根目录
 * 2. 在浏览器中访问：https://your-whmcs.com/proxmox_manual_fix.php
 * 3. 输入服务ID进行修复
 */

// 安全检查 - 只允许管理员访问
session_start();
require_once 'init.php';

use WHMCS\Database\Capsule;
use WHMCS\Authentication\CurrentUser;

// 检查管理员权限
$currentUser = new CurrentUser();
if (!$currentUser->isAuthenticatedAdmin()) {
    die('Access Denied: Admin authentication required');
}

$message = '';
$error = '';

// 处理表单提交
if ($_POST) {
    if (isset($_POST['action'])) {
        switch ($_POST['action']) {
            case 'fix_service':
                $serviceId = (int)$_POST['service_id'];
                if ($serviceId > 0) {
                    $result = fixSingleService($serviceId);
                    if ($result['success']) {
                        $message = $result['message'];
                    } else {
                        $error = $result['message'];
                    }
                }
                break;
                
            case 'fix_all':
                $result = fixAllServices();
                if ($result['success']) {
                    $message = $result['message'];
                } else {
                    $error = $result['message'];
                }
                break;
                
            case 'check_all':
                $result = checkAllServices();
                $message = $result['message'];
                break;
        }
    }
}

/**
 * 修复单个服务
 */
function fixSingleService($serviceId) {
    try {
        // 检查服务是否存在且为Proxmox服务
        $service = Capsule::table('tblhosting as h')
            ->join('tblproducts as p', 'h.packageid', '=', 'p.id')
            ->where('h.id', $serviceId)
            ->where('p.servertype', 'proxmoxVPS')
            ->first(['h.id', 'h.userid', 'h.domain']);
            
        if (!$service) {
            return ['success' => false, 'message' => "Service #{$serviceId} not found or not a Proxmox service"];
        }
        
        // 检查当前状态
        $proxmoxService = Capsule::table('mg_proxmoxaddon_services')
            ->where('service_id', $serviceId)
            ->first(['client_id']);
            
        if ($proxmoxService && $proxmoxService->client_id == $service->userid) {
            return ['success' => true, 'message' => "Service #{$serviceId} ({$service->domain}) is already consistent"];
        }
        
        // 执行修复
        $fixed = syncProxmoxData($serviceId, $service->userid);
        
        return ['success' => true, 'message' => "Service #{$serviceId} ({$service->domain}) has been fixed"];
        
    } catch (Exception $e) {
        return ['success' => false, 'message' => "Error fixing service #{$serviceId}: " . $e->getMessage()];
    }
}

/**
 * 修复所有不一致的服务
 */
function fixAllServices() {
    try {
        $inconsistentServices = getInconsistentServices();
        
        if (empty($inconsistentServices)) {
            return ['success' => true, 'message' => 'No inconsistent services found'];
        }
        
        $fixedCount = 0;
        $errors = [];
        
        foreach ($inconsistentServices as $service) {
            try {
                syncProxmoxData($service->service_id, $service->correct_client_id);
                $fixedCount++;
            } catch (Exception $e) {
                $errors[] = "Service #{$service->service_id}: " . $e->getMessage();
            }
        }
        
        $message = "Fixed {$fixedCount} services";
        if (!empty($errors)) {
            $message .= ". Errors: " . implode('; ', $errors);
        }
        
        return ['success' => true, 'message' => $message];
        
    } catch (Exception $e) {
        return ['success' => false, 'message' => "Error: " . $e->getMessage()];
    }
}

/**
 * 检查所有服务状态
 */
function checkAllServices() {
    try {
        $inconsistentServices = getInconsistentServices();
        
        if (empty($inconsistentServices)) {
            return ['success' => true, 'message' => 'All Proxmox services are consistent'];
        }
        
        $count = count($inconsistentServices);
        $details = [];
        
        foreach ($inconsistentServices as $service) {
            $details[] = "Service #{$service->service_id} ({$service->domain}): " .
                        "WHMCS client #{$service->correct_client_id} vs " .
                        "Proxmox client #" . ($service->current_client_id ?: 'unmapped');
        }
        
        return ['success' => true, 'message' => "Found {$count} inconsistent services:\n" . implode("\n", $details)];
        
    } catch (Exception $e) {
        return ['success' => false, 'message' => "Error: " . $e->getMessage()];
    }
}

/**
 * 获取不一致的服务列表
 */
function getInconsistentServices() {
    return Capsule::table('tblhosting as h')
        ->join('tblproducts as p', 'h.packageid', '=', 'p.id')
        ->leftJoin('mg_proxmoxaddon_services as ps', 'h.id', '=', 'ps.service_id')
        ->where('p.servertype', 'proxmoxVPS')
        ->where(function($query) {
            $query->whereNull('ps.service_id')
                  ->orWhere('ps.client_id', '!=', Capsule::raw('h.userid'));
        })
        ->select([
            'h.id as service_id',
            'h.userid as correct_client_id',
            'h.domain',
            'ps.client_id as current_client_id'
        ])
        ->get();
}

/**
 * 同步Proxmox数据（简化版）
 */
function syncProxmoxData($serviceId, $clientId) {
    // 更新服务映射
    Capsule::table('mg_proxmoxaddon_services')
        ->updateOrInsert(
            ['service_id' => $serviceId],
            ['client_id' => $clientId, 'updated_at' => date('Y-m-d H:i:s')]
        );
    
    // 确保用户映射存在
    Capsule::table('mg_proxmoxaddon_users')
        ->updateOrInsert(
            ['client_id' => $clientId],
            ['realm' => 'pve', 'pve_user' => 'vm' . $clientId, 'updated_at' => date('Y-m-d H:i:s')]
        );
    
    // 更新相关表
    $tables = ['mg_proxmoxaddon_vms', 'mg_proxmoxaddon_ips', 'ProxmoxAddon_Backups'];
    foreach ($tables as $table) {
        try {
            Capsule::table($table)
                ->where('service_id', $serviceId)
                ->update(['client_id' => $clientId]);
        } catch (Exception $e) {
            // 表可能不存在，忽略错误
        }
    }
    
    // 清理缓存
    try {
        Capsule::table('ProxmoxAddon_ModuleCache')->truncate();
    } catch (Exception $e) {
        // 忽略错误
    }
    
    logActivity("ProxmoxManualFix: Fixed service #{$serviceId} → client #{$clientId}");
}

?>
<!DOCTYPE html>
<html>
<head>
    <title>Proxmox Manual Fix Tool</title>
    <style>
        body { font-family: Arial, sans-serif; margin: 20px; }
        .container { max-width: 800px; margin: 0 auto; }
        .message { padding: 10px; margin: 10px 0; border-radius: 4px; }
        .success { background-color: #d4edda; color: #155724; border: 1px solid #c3e6cb; }
        .error { background-color: #f8d7da; color: #721c24; border: 1px solid #f5c6cb; }
        .form-group { margin: 15px 0; }
        label { display: block; margin-bottom: 5px; font-weight: bold; }
        input[type="number"] { padding: 8px; border: 1px solid #ddd; border-radius: 4px; width: 200px; }
        button { padding: 10px 20px; margin: 5px; border: none; border-radius: 4px; cursor: pointer; }
        .btn-primary { background-color: #007bff; color: white; }
        .btn-warning { background-color: #ffc107; color: black; }
        .btn-info { background-color: #17a2b8; color: white; }
        .btn:hover { opacity: 0.8; }
        pre { background-color: #f8f9fa; padding: 10px; border-radius: 4px; white-space: pre-wrap; }
    </style>
</head>
<body>
    <div class="container">
        <h1>Proxmox Manual Fix Tool</h1>
        <p>This tool helps fix Proxmox data inconsistencies after WHMCS ownership transfers.</p>
        
        <?php if ($message): ?>
            <div class="message success"><?php echo nl2br(htmlspecialchars($message)); ?></div>
        <?php endif; ?>
        
        <?php if ($error): ?>
            <div class="message error"><?php echo htmlspecialchars($error); ?></div>
        <?php endif; ?>
        
        <h2>Fix Single Service</h2>
        <form method="post">
            <input type="hidden" name="action" value="fix_service">
            <div class="form-group">
                <label for="service_id">Service ID:</label>
                <input type="number" id="service_id" name="service_id" required>
            </div>
            <button type="submit" class="btn-primary">Fix Service</button>
        </form>
        
        <h2>Batch Operations</h2>
        <form method="post" style="display: inline;">
            <input type="hidden" name="action" value="check_all">
            <button type="submit" class="btn-info">Check All Services</button>
        </form>
        
        <form method="post" style="display: inline;">
            <input type="hidden" name="action" value="fix_all">
            <button type="submit" class="btn-warning" onclick="return confirm('Are you sure you want to fix all inconsistent services?')">Fix All Services</button>
        </form>
        
        <h2>Instructions</h2>
        <ol>
            <li>Use "Check All Services" to see which services have inconsistent data</li>
            <li>Use "Fix Single Service" to fix a specific service by ID</li>
            <li>Use "Fix All Services" to automatically fix all inconsistent services</li>
            <li>Check WHMCS Activity Log for detailed operation logs</li>
        </ol>
        
        <p><strong>Note:</strong> Always backup your database before making changes!</p>
    </div>
</body>
</html>
